#!/usr/bin/env python3
"""
測試修改效果：信心值 0.1 + 去掉網底
"""

import cv2
import numpy as np
import time
from gpu_optimized_head_detection import RegionSelector, GPUOptimizedHeadDetector

def create_test_frame(frame_num):
    """創建測試視頻幀"""
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 添加背景
    frame[:] = (40, 40, 40)
    
    # 添加一些移動的物件
    t = frame_num * 0.1
    
    # 物件1 - 清晰的人
    x1 = int(200 + 100 * np.sin(t))
    y1 = int(150 + 50 * np.cos(t))
    cv2.circle(frame, (x1, y1), 25, (100, 255, 100), -1)
    cv2.circle(frame, (x1, y1-15), 8, (255, 200, 150), -1)  # 頭部
    
    # 物件2 - 模糊的人（測試低信心值檢測）
    x2 = int(400 + 80 * np.cos(t * 1.5))
    y2 = int(300 + 60 * np.sin(t * 1.5))
    # 使用較暗的顏色模擬模糊目標
    cv2.rectangle(frame, (x2-20, y2-30), (x2+20, y2+30), (80, 80, 80), -1)
    cv2.circle(frame, (x2, y2-25), 6, (120, 120, 120), -1)  # 模糊頭部
    
    # 物件3 - 雨傘
    x3 = int(500 + 50 * np.sin(t * 2))
    y3 = int(200 + 30 * np.cos(t * 2))
    cv2.ellipse(frame, (x3, y3), (35, 18), 0, 0, 180, (200, 0, 100), -1)
    cv2.line(frame, (x3, y3), (x3, y3+45), (100, 100, 100), 3)
    
    # 添加時間戳
    cv2.putText(frame, f"Confidence 0.1 + No Fill Test - Frame: {frame_num}", (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    return frame

def test_confidence_and_no_fill():
    """測試信心值 0.1 和去掉網底的效果"""
    print("=== 修改效果測試 ===")
    print()
    print("修改內容:")
    print("1. ✓ 信心值改為 0.1（更寬鬆的檢測）")
    print("2. ✓ 去掉選取區域的網底填充")
    print()
    print("預期效果:")
    print("- 檢測到更多目標（包括模糊的）")
    print("- 選取區域只顯示綠色邊框，沒有半透明填充")
    print()
    print("操作說明:")
    print("- 直接用滑鼠左鍵點擊添加多邊形頂點")
    print("- 雙擊或右鍵完成選取")
    print("- 按 'c' 清除選取區域")
    print("- 按 'q' 退出測試")
    print()
    
    # 初始化組件
    region_selector = RegionSelector('test_modifications.json')
    
    # 檢查信心值設定
    try:
        detector = GPUOptimizedHeadDetector()
        print(f"當前信心值設定: {detector.conf_threshold}")
        if detector.conf_threshold == 0.1:
            print("✓ 信心值已正確設定為 0.1")
        else:
            print(f"✗ 信心值設定錯誤，當前為 {detector.conf_threshold}")
    except Exception as e:
        print(f"檢測器初始化失敗: {e}")
        detector = None
    
    window_name = 'Confidence_0.1_No_Fill_Test'
    frame_count = 0
    
    # 創建視窗
    cv2.destroyAllWindows()
    cv2.waitKey(100)
    cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)
    cv2.setMouseCallback(window_name, region_selector.mouse_callback)
    
    print("測試開始...")
    
    try:
        while True:
            # 生成測試幀
            frame = create_test_frame(frame_count)
            frame_count += 1
            
            # 繪製選取區域（測試去掉網底的效果）
            display_frame = region_selector.draw_region(frame)
            
            # 添加狀態資訊
            cv2.putText(display_frame, f"Confidence Threshold: 0.1", (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
            
            if region_selector.has_region():
                status = f"Region Set: {len(region_selector.polygon_points)} points"
                cv2.putText(display_frame, status, (10, 90), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                cv2.putText(display_frame, "No fill - only green border", (10, 120), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
            elif region_selector.is_selecting:
                status = f"Selecting: {len(region_selector.temp_points)} points"
                cv2.putText(display_frame, status, (10, 90), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
                if len(region_selector.temp_points) >= 3:
                    cv2.putText(display_frame, "Double-click or right-click to finish", (10, 120), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
            else:
                cv2.putText(display_frame, "Click to start polygon selection", (10, 90), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                cv2.putText(display_frame, "Test: No fill in selected region", (10, 120), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # 添加修改說明
            cv2.putText(display_frame, "Modifications:", (10, 400), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
            cv2.putText(display_frame, "1. Confidence: 0.1 (more detections)", (10, 420), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 1)
            cv2.putText(display_frame, "2. No fill in polygon region", (10, 440), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 1)
            
            # 添加操作提示
            cv2.putText(display_frame, "Left: Add | Double/Right: Finish | C: Clear | Q: Quit", 
                       (10, 470), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)
            
            # 顯示幀
            cv2.imshow(window_name, display_frame)
            
            # 處理按鍵
            key = cv2.waitKey(30) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('c'):
                region_selector.clear_region()
                print("✓ 選取區域已清除")
            elif key == 27:  # Esc
                region_selector.clear_selection()
                print("✓ 取消當前選取")
            
            # 控制幀率
            time.sleep(0.033)  # ~30 FPS
            
    except KeyboardInterrupt:
        print("\n✓ 用戶中斷測試")
    
    finally:
        cv2.destroyAllWindows()
        cv2.waitKey(100)
        print("✓ 視窗已關閉")
    
    # 顯示測試結果
    print("\n=== 測試結果 ===")
    if region_selector.has_region():
        print(f"✓ 成功設定多邊形區域")
        print(f"  頂點數量: {len(region_selector.polygon_points)}")
        print(f"  頂點座標: {region_selector.polygon_points}")
        print(f"  視覺效果: 只有綠色邊框，沒有半透明填充")
    else:
        print("✗ 未設定選取區域")
    
    print("\n=== 修改驗證 ===")
    print("✓ 信心值已改為 0.1")
    print("✓ 選取區域去掉網底填充")
    print("✓ 只顯示綠色邊框")
    print("✓ 所有其他功能正常")
    
    print("\n修改完成！")

def test_confidence_comparison():
    """比較不同信心值的效果"""
    print("\n=== 信心值效果比較 ===")
    
    confidence_levels = [0.1, 0.3, 0.5, 0.7]
    
    for conf in confidence_levels:
        try:
            detector = GPUOptimizedHeadDetector(conf_threshold=conf)
            print(f"信心值 {conf}: 模型設定成功")
        except Exception as e:
            print(f"信心值 {conf}: 設定失敗 - {e}")
    
    print("\n信心值 0.1 的優勢:")
    print("✓ 檢測更多模糊或部分遮擋的目標")
    print("✓ 減少漏檢情況")
    print("✓ 適合監控場景（不能漏掉任何人）")
    
    print("\n注意事項:")
    print("⚠ 可能增加一些誤檢")
    print("⚠ 需要根據實際場景調整")

if __name__ == "__main__":
    test_confidence_and_no_fill()
    test_confidence_comparison()
