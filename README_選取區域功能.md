# GPU優化頭部檢測系統 - 多邊形選取區域功能

## 新增功能概述

本次更新為 `gpu_optimized_head_detection.py` 添加了多邊形選取區域功能，讓您可以指定任意形狀的區域進行頭部檢測，提高檢測效率並減少誤檢。

## 主要功能

### 1. 多邊形選取區域設定
- **點擊式多邊形選取**: 用滑鼠左鍵逐點點擊定義多邊形頂點
- **不規則形狀支援**: 支援任意多邊形形狀，不限於矩形
- **即時預覽**: 選取過程中即時顯示已連接的線段和頂點
- **智能完成**: 雙擊或點擊起始點自動封閉多邊形
- **自動儲存**: 選取完成後自動儲存到 `detection_region.json` 檔案
- **自動載入**: 下次執行時自動載入上次儲存的選取區域
- **向後相容**: 自動轉換舊版矩形格式為多邊形格式

### 2. 多邊形區域檢測
- **精確限制檢測範圍**: 只在多邊形區域內進行頭部檢測
- **遮罩處理**: 使用多邊形遮罩精確裁切檢測區域
- **座標自動調整**: 檢測結果座標自動調整回原始畫面座標系
- **效能提升**: 減少檢測範圍可大幅提高處理速度

### 3. 視覺化顯示
- **多邊形邊框**: 綠色邊框顯示當前選取的多邊形區域
- **半透明填充**: 多邊形內部顯示半透明綠色填充
- **頂點標記**: 選取過程中顯示編號的頂點標記
- **即時連線**: 顯示已連接的線段和預覽線段
- **智能提示**: 當滑鼠接近起始點時顯示完成提示

## 操作說明

### 基本操作
1. **啟動程式**: 執行 `python gpu_optimized_head_detection.py`
2. **進入選取模式**: 按 `r` 鍵進入多邊形選取區域模式
3. **選取多邊形頂點**: 用滑鼠左鍵逐點點擊定義多邊形頂點
4. **完成選取**: 雙擊、點擊起始點或按 `Enter` 鍵完成選取
5. **取消選取**: 按 `Esc` 鍵取消當前選取
6. **退出選取模式**: 按 `r` 鍵退出選取區域模式
7. **清除區域**: 按 `c` 鍵清除當前選取區域
8. **退出程式**: 按 `q` 鍵退出

### 按鍵說明
- `r`: 進入/退出多邊形選取區域模式
- `Enter`: 完成多邊形選取（需至少3個頂點）
- `Esc`: 取消當前選取
- `c`: 清除選取區域
- `q`: 退出程式
- `s`: 儲存當前畫面
- `m`: 顯示GPU記憶體資訊

### 滑鼠操作
- **左鍵點擊**: 添加多邊形頂點
- **雙擊**: 完成多邊形選取
- **點擊起始點**: 封閉多邊形並完成選取
- **滑鼠移動**: 顯示預覽線段到當前位置

### 選取技巧
- **最少頂點**: 至少需要3個頂點才能形成有效多邊形
- **精確選取**: 可以選取任意複雜的多邊形形狀
- **起始點提示**: 當滑鼠接近起始點時會顯示黃色提示線
- **頂點編號**: 每個頂點都會顯示編號，方便追蹤選取進度

## 檔案說明

### 新增檔案
- `detection_region.json`: 儲存選取區域設定的檔案
- `test_region_selection.py`: 測試選取區域功能的腳本
- `README_選取區域功能.md`: 本說明文件

### 修改的類別

#### RegionSelector 類別
- 管理選取區域的所有功能
- 處理滑鼠事件和區域選取
- 負責區域設定的儲存和載入

#### GPUOptimizedHeadDetector 類別
- 新增 `region_selector` 參數
- 支援在選取區域內進行檢測
- 自動調整檢測結果座標

#### GPUOptimizedRTSPProcessor 類別
- 整合選取區域功能
- 處理選取模式的顯示和按鍵
- 支援即時區域設定

## 設定檔格式

`detection_region.json` 檔案格式（新版多邊形格式）：
```json
{
  "polygon_points": [[x1, y1], [x2, y2], [x3, y3], ...],
  "type": "polygon",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

其中：
- `polygon_points`: 多邊形頂點座標列表 [[x1, y1], [x2, y2], ...]
- `type`: 區域類型，固定為 "polygon"
- `timestamp`: 設定時間戳記

### 向後相容性
程式會自動檢測並轉換舊版矩形格式：
```json
{
  "region": [x1, y1, x2, y2],
  "timestamp": "2024-01-01T12:00:00.000000"
}
```
舊格式會自動轉換為4個頂點的多邊形並儲存為新格式。

## 測試功能

執行測試腳本：
```bash
python test_region_selection.py
```

測試腳本功能：
- 測試選取區域的基本功能
- 驗證座標調整功能
- 測試區域儲存和載入

## 注意事項

1. **區域大小**: 選取區域必須大於 50x50 像素才會生效
2. **座標系統**: 所有座標都是基於原始畫面的絕對座標
3. **效能影響**: 選取較小區域可以提高檢測速度
4. **檔案權限**: 確保程式有權限讀寫 `detection_region.json` 檔案

## 故障排除

### 常見問題
1. **選取區域無效**: 確保選取區域大小足夠（>50x50像素）
2. **設定檔無法儲存**: 檢查檔案權限和磁碟空間
3. **座標偏移**: 確保在正確的視窗中進行選取操作

### 除錯模式
程式會在控制台輸出詳細的操作日誌，包括：
- 選取區域的座標資訊
- 檔案儲存和載入狀態
- 檢測結果的座標調整過程

## 效能優化建議

1. **合理選取區域**: 選取包含主要檢測目標的最小區域
2. **避免頻繁切換**: 減少進出選取模式的頻率
3. **定期清理**: 不需要時及時清除選取區域以使用全畫面檢測
