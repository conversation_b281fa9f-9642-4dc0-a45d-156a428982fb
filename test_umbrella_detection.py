#!/usr/bin/env python3
"""
測試雨傘檢測功能
"""

import cv2
import numpy as np
import time
from gpu_optimized_head_detection import GPUOptimizedHeadDetector, RegionSelector

def create_test_frame_with_umbrellas(frame_num):
    """創建包含雨傘的測試視頻幀"""
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 添加背景
    frame[:] = (50, 50, 50)
    
    t = frame_num * 0.1
    
    # 模擬人員（矩形身體 + 圓形頭部）
    person_x = int(150 + 50 * np.sin(t))
    person_y = 200
    # 身體
    cv2.rectangle(frame, (person_x-15, person_y), (person_x+15, person_y+60), (100, 255, 100), -1)
    # 頭部
    cv2.circle(frame, (person_x, person_y-10), 12, (255, 200, 150), -1)
    
    # 模擬雨傘1（半圓形）
    umbrella1_x = int(350 + 30 * np.cos(t))
    umbrella1_y = 150
    # 雨傘頂部（半圓）
    cv2.ellipse(frame, (umbrella1_x, umbrella1_y), (40, 20), 0, 0, 180, (0, 100, 200), -1)
    # 雨傘柄
    cv2.line(frame, (umbrella1_x, umbrella1_y), (umbrella1_x, umbrella1_y+50), (100, 100, 100), 3)
    
    # 模擬雨傘2（不同顏色）
    umbrella2_x = int(500 + 40 * np.sin(t * 1.5))
    umbrella2_y = 180
    # 雨傘頂部
    cv2.ellipse(frame, (umbrella2_x, umbrella2_y), (35, 18), 0, 0, 180, (200, 0, 100), -1)
    # 雨傘柄
    cv2.line(frame, (umbrella2_x, umbrella2_y), (umbrella2_x, umbrella2_y+45), (100, 100, 100), 3)
    
    # 模擬另一個人員
    person2_x = int(100 + 60 * np.cos(t * 0.8))
    person2_y = 300
    # 身體
    cv2.rectangle(frame, (person2_x-12, person2_y), (person2_x+12, person2_y+55), (255, 100, 100), -1)
    # 頭部
    cv2.circle(frame, (person2_x, person2_y-8), 10, (255, 200, 150), -1)
    
    # 添加時間戳和說明
    cv2.putText(frame, f"Umbrella Detection Test - Frame: {frame_num}", (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    cv2.putText(frame, "Green=Person, Blue/Purple=Umbrella", (10, 460), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    return frame

class MockYOLOResults:
    """模擬 YOLO 檢測結果"""
    def __init__(self, detections):
        self.detections = detections
    
    def pandas(self):
        class MockPandas:
            def __init__(self, detections):
                self.xyxy = [MockDataFrame(detections)]
        return MockPandas(self.detections)

class MockDataFrame:
    """模擬 pandas DataFrame"""
    def __init__(self, detections):
        self.data = detections
    
    def __getitem__(self, condition):
        if hasattr(condition, '__call__'):  # 如果是函數條件
            return MockDataFrame([d for d in self.data if condition(d)])
        elif condition == 'class':
            return MockSeries([d['class'] for d in self.data])
        else:
            return MockDataFrame(self.data)
    
    def iterrows(self):
        for i, detection in enumerate(self.data):
            yield i, MockRow(detection)

class MockSeries:
    """模擬 pandas Series"""
    def __init__(self, data):
        self.data = data
    
    def __eq__(self, value):
        return [d == value for d in self.data]

class MockRow:
    """模擬 pandas Row"""
    def __init__(self, data):
        self.data = data
    
    def __getitem__(self, key):
        return self.data[key]

def create_mock_detector():
    """創建模擬檢測器"""
    class MockDetector:
        def __init__(self):
            self.device = 'cpu'
            self.detect_umbrella = True
            self.region_selector = None
            self.coco_classes = {0: 'person', 25: 'umbrella'}
        
        def detect_heads_batch(self, frames):
            results = []
            for frame in frames:
                # 模擬檢測結果：2個人員 + 2個雨傘
                mock_detections = [
                    # 人員1
                    {'class': 0, 'xmin': 135, 'ymin': 180, 'xmax': 165, 'ymax': 260, 'confidence': 0.85},
                    # 人員2  
                    {'class': 0, 'xmin': 88, 'ymin': 292, 'xmax': 112, 'ymax': 355, 'confidence': 0.80},
                    # 雨傘1
                    {'class': 25, 'xmin': 310, 'ymin': 130, 'xmax': 390, 'ymax': 170, 'confidence': 0.75},
                    # 雨傘2
                    {'class': 25, 'xmin': 465, 'ymin': 162, 'xmax': 535, 'ymax': 198, 'confidence': 0.70}
                ]
                
                mock_results = MockYOLOResults(mock_detections)
                head_count, head_bboxes, annotated_frame = self._process_single_frame_result(
                    frame, mock_results, frame)
                results.append((head_count, head_bboxes, annotated_frame))
            
            return results
        
        def _process_single_frame_result(self, detection_frame, results, original_frame=None):
            """簡化的檢測結果處理"""
            if original_frame is None:
                original_frame = detection_frame
                
            detections = results.pandas().xyxy[0]
            head_bboxes = []
            detection_types = []
            annotated_frame = original_frame.copy()
            
            # 處理人員檢測
            person_detections = [d for d in detections.data if d['class'] == 0]
            for detection in person_detections:
                person_bbox = [detection['xmin'], detection['ymin'], 
                             detection['xmax'], detection['ymax']]
                confidence = detection['confidence']
                
                # 簡化的頭部區域提取
                head_bbox = self._extract_head_region(detection_frame, person_bbox)
                head_bboxes.append(head_bbox + [confidence])
                detection_types.append('person')
            
            # 處理雨傘檢測
            if self.detect_umbrella:
                umbrella_detections = [d for d in detections.data if d['class'] == 25]
                for detection in umbrella_detections:
                    umbrella_bbox = [detection['xmin'], detection['ymin'],
                                   detection['xmax'], detection['ymax']]
                    confidence = detection['confidence']
                    
                    umbrella_head_bbox = self._extract_umbrella_head_region(detection_frame, umbrella_bbox)
                    head_bboxes.append(umbrella_head_bbox + [confidence])
                    detection_types.append('umbrella')
            
            # 繪製檢測結果
            head_count = len(head_bboxes)
            person_count = detection_types.count('person')
            umbrella_count = detection_types.count('umbrella')
            
            for i, (x1, y1, x2, y2, confidence) in enumerate(head_bboxes):
                detection_type = detection_types[i]
                if detection_type == 'umbrella':
                    color = (255, 165, 0)  # 橙色表示雨傘
                    type_label = 'U'
                else:
                    color = (0, 0, 255)    # 紅色表示人員
                    type_label = 'P'
                
                # 繪製邊界框
                cv2.rectangle(annotated_frame, (int(x1), int(y1)), (int(x2), int(y2)), color, 2)
                
                # 標籤
                label = f'{i+1}{type_label}'
                cv2.putText(annotated_frame, label, (int(x1), int(y1)-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            
            # 添加統計資訊
            if umbrella_count > 0:
                count_text = f'Total: {head_count} (P:{person_count} U:{umbrella_count})'
                cv2.putText(annotated_frame, count_text, (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                cv2.putText(annotated_frame, 'P=Person U=Umbrella', (10, 90), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            else:
                count_text = f'Head Count: {head_count}'
                cv2.putText(annotated_frame, count_text, (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            
            return head_count, head_bboxes, annotated_frame
        
        def _extract_head_region(self, frame, person_bbox, head_ratio=0.15):
            """從人體檢測框中提取頭部區域"""
            x1, y1, x2, y2 = person_bbox
            person_height = y2 - y1
            person_width = x2 - x1
            
            head_height = int(person_height * head_ratio)
            head_width_margin = int(person_width * 0.1)
            
            head_x1 = max(0, x1 + head_width_margin)
            head_y1 = max(0, y1)
            head_x2 = min(frame.shape[1], x2 - head_width_margin)
            head_y2 = min(frame.shape[0], y1 + head_height)
            
            return [head_x1, head_y1, head_x2, head_y2]
        
        def _extract_umbrella_head_region(self, frame, umbrella_bbox):
            """從雨傘檢測框中提取假想的頭部區域"""
            x1, y1, x2, y2 = umbrella_bbox
            umbrella_width = x2 - x1
            
            head_size = max(20, int(umbrella_width * 0.25))
            center_x = (x1 + x2) // 2
            head_top_y = y2
            
            head_x1 = max(0, center_x - head_size // 2)
            head_y1 = max(0, head_top_y)
            head_x2 = min(frame.shape[1], center_x + head_size // 2)
            head_y2 = min(frame.shape[0], head_top_y + head_size)
            
            return [head_x1, head_y1, head_x2, head_y2]
    
    return MockDetector()

def test_umbrella_detection():
    """測試雨傘檢測功能"""
    print("=== 雨傘檢測功能測試 ===")
    print()
    print("功能說明:")
    print("✓ 檢測人員（紅色邊框，標記 P）")
    print("✓ 檢測雨傘（橙色邊框，標記 U）")
    print("✓ 將雨傘也當作一個人來計算")
    print("✓ 顯示分別計數：P=人員數量 U=雨傘數量")
    print()
    print("操作說明:")
    print("- 按 'q' 退出測試")
    print("- 按 's' 保存當前幀")
    print()
    
    # 創建模擬檢測器
    detector = create_mock_detector()
    
    window_name = 'Umbrella Detection Test'
    cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)
    
    frame_count = 0
    
    print("測試開始...")
    print("預期結果：應該檢測到 2 個人員 + 2 個雨傘 = 總共 4 個目標")
    
    try:
        while True:
            # 生成測試幀
            frame = create_test_frame_with_umbrellas(frame_count)
            frame_count += 1
            
            # 執行檢測
            results = detector.detect_heads_batch([frame])
            head_count, head_bboxes, annotated_frame = results[0]
            
            # 顯示結果
            cv2.imshow(window_name, annotated_frame)
            
            # 處理按鍵
            key = cv2.waitKey(100) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                save_path = f"umbrella_test_frame_{frame_count}.jpg"
                cv2.imwrite(save_path, annotated_frame)
                print(f"✓ 保存測試幀到: {save_path}")
            
            # 控制幀率
            time.sleep(0.1)
            
    except KeyboardInterrupt:
        print("\n✓ 用戶中斷測試")
    
    finally:
        cv2.destroyAllWindows()
        print("✓ 視窗已關閉")
    
    print("\n=== 測試完成 ===")
    print("如果看到以下結果，表示雨傘檢測功能正常:")
    print("- 紅色邊框標記 P：人員檢測")
    print("- 橙色邊框標記 U：雨傘檢測")
    print("- 總計數包含人員和雨傘")
    print("- 顯示格式：Total: 4 (P:2 U:2)")

if __name__ == "__main__":
    test_umbrella_detection()
