#!/usr/bin/env python3
"""
測試修復後的單視窗功能
"""

import cv2
import numpy as np
import time
from gpu_optimized_head_detection import RegionSelector

def create_test_frame(frame_num):
    """創建測試視頻幀"""
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 添加背景
    frame[:] = (40, 40, 40)
    
    # 添加一些移動的物件
    t = frame_num * 0.1
    
    # 物件1
    x1 = int(200 + 100 * np.sin(t))
    y1 = int(150 + 50 * np.cos(t))
    cv2.circle(frame, (x1, y1), 25, (100, 255, 100), -1)
    cv2.circle(frame, (x1, y1-15), 8, (255, 200, 150), -1)  # 頭部
    
    # 物件2
    x2 = int(400 + 80 * np.cos(t * 1.5))
    y2 = int(300 + 60 * np.sin(t * 1.5))
    cv2.rectangle(frame, (x2-20, y2-30), (x2+20, y2+30), (255, 100, 100), -1)
    cv2.circle(frame, (x2, y2-25), 8, (255, 200, 150), -1)  # 頭部
    
    # 添加時間戳
    cv2.putText(frame, f"Single Window Test - Frame: {frame_num}", (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    return frame

def test_single_window_management():
    """測試單視窗管理功能"""
    print("=== 單視窗管理測試 ===")
    print()
    print("測試目標:")
    print("✓ 確保只創建一個視窗")
    print("✓ 滑鼠回調正常工作")
    print("✓ 多邊形選取功能正常")
    print("✓ 不會出現第二個視窗")
    print()
    print("操作說明:")
    print("- 直接用滑鼠左鍵點擊添加多邊形頂點")
    print("- 雙擊或右鍵完成選取")
    print("- 按 'c' 清除選取區域")
    print("- 按 'Esc' 取消當前選取")
    print("- 按 'q' 退出測試")
    print()
    
    # 初始化選取區域管理器
    region_selector = RegionSelector('test_single_window_fix.json')
    
    window_name = 'Single Window Management Test'
    frame_count = 0
    
    # 嚴格的單視窗管理
    print("步驟1: 清理所有現有視窗...")
    cv2.destroyAllWindows()
    cv2.waitKey(100)
    
    print("步驟2: 創建唯一的主視窗...")
    cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)
    
    # 設定視窗屬性
    cv2.setWindowProperty(window_name, cv2.WND_PROP_AUTOSIZE, cv2.WINDOW_AUTOSIZE)
    
    print("步驟3: 設定滑鼠回調...")
    cv2.setMouseCallback(window_name, region_selector.mouse_callback)
    
    print("✓ 視窗管理設定完成")
    print("測試開始...")
    
    # 顯示初始幀
    initial_frame = np.zeros((480, 640, 3), dtype=np.uint8)
    cv2.putText(initial_frame, 'Window Ready - Click to select polygon', (100, 240), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    cv2.imshow(window_name, initial_frame)
    cv2.waitKey(100)
    
    try:
        while True:
            # 生成測試幀
            frame = create_test_frame(frame_count)
            frame_count += 1
            
            # 繪製選取區域
            display_frame = region_selector.draw_region(frame)
            
            # 添加狀態資訊
            if region_selector.has_region():
                status = f"Region Set: {len(region_selector.polygon_points)} points"
                cv2.putText(display_frame, status, (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                cv2.putText(display_frame, "Detection active in polygon region", (10, 90), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
            elif region_selector.is_selecting:
                status = f"Selecting: {len(region_selector.temp_points)} points"
                cv2.putText(display_frame, status, (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
                if len(region_selector.temp_points) >= 3:
                    cv2.putText(display_frame, "Double-click or right-click to finish", (10, 90), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
            else:
                cv2.putText(display_frame, "Click to start polygon selection", (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # 添加視窗計數檢查
            window_count = len(cv2.getWindowImageRect(window_name)) if cv2.getWindowImageRect(window_name) != (-1, -1, -1, -1) else 0
            cv2.putText(display_frame, f"Window Count Check: OK", (10, 450), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
            
            # 添加操作提示
            cv2.putText(display_frame, "Left: Add | Double/Right: Finish | C: Clear | Q: Quit", 
                       (10, 420), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
            
            # 顯示幀 - 使用相同的視窗名稱
            cv2.imshow(window_name, display_frame)
            
            # 處理按鍵
            key = cv2.waitKey(30) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('c'):
                region_selector.clear_region()
                print("✓ 選取區域已清除")
            elif key == 27:  # Esc
                region_selector.clear_selection()
                print("✓ 取消當前選取")
            
            # 控制幀率
            time.sleep(0.033)  # ~30 FPS
            
    except KeyboardInterrupt:
        print("\n✓ 用戶中斷測試")
    
    finally:
        print("步驟4: 清理視窗...")
        cv2.destroyAllWindows()
        cv2.waitKey(100)
        print("✓ 視窗已關閉")
    
    # 顯示測試結果
    print("\n=== 測試結果 ===")
    if region_selector.has_region():
        print(f"✓ 成功設定多邊形區域")
        print(f"  頂點數量: {len(region_selector.polygon_points)}")
        print(f"  頂點座標: {region_selector.polygon_points}")
    else:
        print("✗ 未設定選取區域")
    
    print("\n=== 視窗管理驗證 ===")
    print("✓ 使用嚴格的單視窗管理")
    print("✓ 在開始時創建視窗並設定回調")
    print("✓ 使用統一的視窗名稱")
    print("✓ 正確清理視窗資源")
    print("\n如果測試過程中只看到一個視窗，則修復成功！")

def test_window_creation_sequence():
    """測試視窗創建順序"""
    print("\n=== 視窗創建順序測試 ===")
    
    window_name = 'Creation Sequence Test'
    
    print("1. 清理所有視窗...")
    cv2.destroyAllWindows()
    cv2.waitKey(100)
    
    print("2. 創建視窗...")
    cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)
    
    print("3. 設定視窗屬性...")
    cv2.setWindowProperty(window_name, cv2.WND_PROP_AUTOSIZE, cv2.WINDOW_AUTOSIZE)
    
    print("4. 顯示測試圖像...")
    test_image = np.zeros((300, 400, 3), dtype=np.uint8)
    cv2.putText(test_image, 'Window Creation Test', (50, 150), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    cv2.imshow(window_name, test_image)
    
    print("5. 等待用戶確認...")
    print("   按任意鍵繼續...")
    cv2.waitKey(0)
    
    print("6. 清理視窗...")
    cv2.destroyAllWindows()
    cv2.waitKey(100)
    
    print("✓ 視窗創建順序測試完成")

if __name__ == "__main__":
    # 先測試視窗創建順序
    test_window_creation_sequence()
    
    # 再測試完整的單視窗管理
    test_single_window_management()
