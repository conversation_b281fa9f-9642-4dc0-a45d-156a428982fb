# GPU優化頭部檢測系統 - 信心值（置信度）說明

## 📍 信心值位置

### 1. 命令行參數定義
**檔案位置：** `gpu_optimized_head_detection.py` 第 820 行

```python
parser.add_argument('--conf-threshold', type=float, default=0.4,
                   help='检测置信度阈值')
```

### 2. 類別初始化
**檔案位置：** `gpu_optimized_head_detection.py` 第 290-302 行

```python
class GPUOptimizedHeadDetector:
    def __init__(self, model_type='person', conf_threshold=0.4, device='cuda', batch_size=1, region_selector=None, detect_umbrella=True):
        """
        GPU优化的头部检测器
        
        Args:
            model_type: 模型类型
            conf_threshold: 置信度阈值  # ← 信心值參數
            device: 运行设备
            batch_size: 批处理大小（GPU优化）
            region_selector: 選取區域管理器
            detect_umbrella: 是否將雨傘也當作一個人來檢測
        """
        self.conf_threshold = conf_threshold  # ← 儲存信心值
```

### 3. 模型設定
**檔案位置：** `gpu_optimized_head_detection.py` 第 369 行

```python
# 模型优化设置
self.model.to(self.device)
self.model.conf = self.conf_threshold  # ← 設定到 YOLOv5 模型
self.model.iou = 0.45  # NMS IoU阈值
self.model.max_det = 1000  # 最大检测数量
```

### 4. 主程式使用
**檔案位置：** `gpu_optimized_head_detection.py` 第 877 行

```python
detector = GPUOptimizedHeadDetector(
    model_type=args.model_type,
    conf_threshold=args.conf_threshold,  # ← 從命令行參數傳入
    device=args.device,
    batch_size=args.batch_size,
    region_selector=region_selector,
    detect_umbrella=detect_umbrella
)
```

## 🎯 信心值的作用

### 1. 檢測過濾
信心值用於過濾檢測結果，只保留置信度高於閾值的檢測：

```python
# YOLOv5 內部會自動過濾低於 conf_threshold 的檢測結果
# 只有 confidence >= conf_threshold 的檢測才會被返回
```

### 2. 檢測結果處理
**檔案位置：** `gpu_optimized_head_detection.py` 第 510, 520, 533, 549 行

```python
# 人員檢測
confidence = detection['confidence']  # ← 獲取檢測的信心值
head_bboxes.append([x1, y1, x2, y2, confidence])  # ← 儲存信心值

# 雨傘檢測
confidence = detection['confidence']  # ← 獲取檢測的信心值
head_bboxes.append(umbrella_head_bbox + [confidence])  # ← 儲存信心值

# 繪製檢測結果
for i, (x1, y1, x2, y2, confidence) in enumerate(head_bboxes):  # ← 使用信心值
    # 繪製邊界框和標籤
```

## ⚙️ 如何設定信心值

### 1. 命令行設定
```bash
# 使用預設值 0.4
python gpu_optimized_head_detection.py

# 設定較高的信心值（更嚴格）
python gpu_optimized_head_detection.py --conf-threshold 0.6

# 設定較低的信心值（更寬鬆）
python gpu_optimized_head_detection.py --conf-threshold 0.3

# 設定非常高的信心值（只保留最確定的檢測）
python gpu_optimized_head_detection.py --conf-threshold 0.8
```

### 2. 程式碼中修改
如果要在程式碼中直接修改預設值：

```python
# 修改命令行參數的預設值
parser.add_argument('--conf-threshold', type=float, default=0.5,  # 改為 0.5
                   help='检测置信度阈值')

# 或者修改類別初始化的預設值
def __init__(self, model_type='person', conf_threshold=0.5, ...):  # 改為 0.5
```

## 📊 信心值對檢測效果的影響

### 信心值範圍：0.0 - 1.0

**0.1 - 0.3（低信心值）：**
- ✅ **優點**：檢測到更多目標，減少漏檢
- ❌ **缺點**：可能產生較多誤檢（假陽性）
- 🎯 **適用**：需要確保不漏掉任何目標的場景

**0.4 - 0.6（中等信心值）：**
- ✅ **優點**：平衡檢測率和準確率
- ✅ **預設值**：程式預設使用 0.4
- 🎯 **適用**：大多數一般應用場景

**0.7 - 0.9（高信心值）：**
- ✅ **優點**：檢測結果更準確，減少誤檢
- ❌ **缺點**：可能漏掉一些不太清晰的目標
- 🎯 **適用**：對準確率要求很高的場景

### 實際效果示例

**信心值 0.3：**
```
檢測結果：10 個目標（可能包含 2-3 個誤檢）
適合：監控場景，不能漏掉任何人
```

**信心值 0.4（預設）：**
```
檢測結果：8 個目標（平衡的檢測效果）
適合：一般應用場景
```

**信心值 0.7：**
```
檢測結果：6 個目標（高準確率，可能漏掉模糊目標）
適合：需要高準確率的統計場景
```

## 🔧 信心值調優建議

### 1. 根據應用場景調整

**安全監控場景：**
```bash
# 使用較低信心值，確保不漏掉任何人
python gpu_optimized_head_detection.py --conf-threshold 0.3
```

**人流統計場景：**
```bash
# 使用中等信心值，平衡準確率和檢測率
python gpu_optimized_head_detection.py --conf-threshold 0.4
```

**精確計數場景：**
```bash
# 使用較高信心值，確保統計準確性
python gpu_optimized_head_detection.py --conf-threshold 0.6
```

### 2. 根據視頻品質調整

**高清視頻：**
- 可以使用較高信心值（0.5-0.7）
- 目標清晰，檢測準確率高

**低解析度視頻：**
- 建議使用較低信心值（0.3-0.4）
- 目標可能模糊，需要更寬鬆的閾值

**夜間或低光照：**
- 建議使用較低信心值（0.2-0.4）
- 圖像品質較差，需要降低要求

## 💡 信心值顯示

### 當前程式中的信心值顯示

雖然程式儲存了每個檢測的信心值，但目前**沒有在界面上顯示**。如果需要顯示信心值，可以修改繪製部分：

```python
# 當前代碼（不顯示信心值）
label = f'{i+1}{type_label}'

# 修改後（顯示信心值）
label = f'{i+1}{type_label}({confidence:.2f})'
```

### 建議的信心值顯示方式

1. **在標籤中顯示**：`1P(0.85)` 表示第1個人員，信心值0.85
2. **顏色編碼**：高信心值用綠色，低信心值用黃色
3. **統計資訊**：顯示平均信心值

## 📁 相關檔案位置總結

| 功能 | 檔案位置 | 行號 |
|------|----------|------|
| 命令行參數定義 | `gpu_optimized_head_detection.py` | 820 |
| 類別參數定義 | `gpu_optimized_head_detection.py` | 290, 302 |
| 模型設定 | `gpu_optimized_head_detection.py` | 369 |
| 主程式使用 | `gpu_optimized_head_detection.py` | 877 |
| 檢測結果處理 | `gpu_optimized_head_detection.py` | 510, 520, 533, 549 |

## 🎯 總結

**信心值的核心作用：**
1. ✅ **過濾檢測結果**：只保留高置信度的檢測
2. ✅ **控制檢測敏感度**：調整檢測的嚴格程度
3. ✅ **平衡準確率和召回率**：根據需求調整平衡點

**預設設定：**
- 預設信心值：`0.4`
- 適用於大多數場景
- 可通過 `--conf-threshold` 參數調整

**調整建議：**
- 需要更多檢測：降低信心值（0.2-0.3）
- 需要更準確：提高信心值（0.6-0.8）
- 一般使用：保持預設值（0.4）
