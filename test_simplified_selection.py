#!/usr/bin/env python3
"""
測試簡化的多邊形選取功能
"""

import cv2
import numpy as np
import time
from gpu_optimized_head_detection import RegionSelector

def create_test_video_frame(frame_num):
    """創建測試視頻幀"""
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 添加背景
    frame[:] = (40, 40, 40)
    
    # 添加一些移動的物件
    t = frame_num * 0.1
    
    # 物件1 - 模擬人
    x1 = int(200 + 100 * np.sin(t))
    y1 = int(150 + 50 * np.cos(t))
    cv2.circle(frame, (x1, y1), 25, (100, 255, 100), -1)
    cv2.circle(frame, (x1, y1-15), 8, (255, 200, 150), -1)  # 頭部
    
    # 物件2 - 模擬人
    x2 = int(400 + 80 * np.cos(t * 1.5))
    y2 = int(300 + 60 * np.sin(t * 1.5))
    cv2.rectangle(frame, (x2-20, y2-30), (x2+20, y2+30), (255, 100, 100), -1)
    cv2.circle(frame, (x2, y2-25), 8, (255, 200, 150), -1)  # 頭部
    
    # 物件3 - 模擬人
    x3 = int(500 + 50 * np.sin(t * 2))
    y3 = int(200 + 30 * np.cos(t * 2))
    cv2.ellipse(frame, (x3, y3), (15, 25), 0, 0, 360, (100, 100, 255), -1)
    cv2.circle(frame, (x3, y3-20), 8, (255, 200, 150), -1)  # 頭部
    
    # 添加時間戳
    cv2.putText(frame, f"Test Frame: {frame_num}", (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    return frame

def test_simplified_polygon_selection():
    """測試簡化的多邊形選取功能"""
    print("=== 簡化多邊形選取功能測試 ===")
    print()
    print("新的簡化操作方式:")
    print("✓ 程式啟動後，滑鼠可直接在視窗上點擊選取")
    print("✓ 左鍵點擊：添加多邊形頂點")
    print("✓ 雙擊或右鍵：完成多邊形選取")
    print("✓ 按 'c' 清除選取區域")
    print("✓ 按 'Esc' 取消當前選取")
    print("✓ 按 'q' 退出測試")
    print()
    
    # 初始化選取區域管理器
    region_selector = RegionSelector('test_simplified_region.json')
    
    window_name = 'Simplified Polygon Selection Test'
    frame_count = 0
    
    # 創建視窗並立即設定滑鼠回調
    cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)
    cv2.setMouseCallback(window_name, region_selector.mouse_callback)
    
    print("✓ 視窗已創建，滑鼠可直接進行多邊形選取")
    print("測試開始...")
    
    try:
        while True:
            # 生成測試幀
            frame = create_test_video_frame(frame_count)
            frame_count += 1
            
            # 繪製選取區域
            display_frame = region_selector.draw_region(frame)
            
            # 添加狀態資訊
            if region_selector.has_region():
                status = f"Region Set: {len(region_selector.polygon_points)} points"
                cv2.putText(display_frame, status, (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                cv2.putText(display_frame, "Detection active in polygon region", (10, 90), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
            elif region_selector.is_selecting:
                status = f"Selecting: {len(region_selector.temp_points)} points"
                cv2.putText(display_frame, status, (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
            else:
                cv2.putText(display_frame, "Click to start polygon selection", (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # 添加操作提示
            cv2.putText(display_frame, "Left: Add point | Double/Right: Finish | C: Clear | Q: Quit", 
                       (10, 450), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
            
            # 顯示幀
            cv2.imshow(window_name, display_frame)
            
            # 處理按鍵
            key = cv2.waitKey(30) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('c'):
                region_selector.clear_region()
                print("✓ 選取區域已清除")
            elif key == 27:  # Esc
                region_selector.clear_selection()
                print("✓ 取消當前選取")
            
            # 控制幀率
            time.sleep(0.033)  # ~30 FPS
            
    except KeyboardInterrupt:
        print("\n✓ 用戶中斷測試")
    
    finally:
        cv2.destroyAllWindows()
        print("✓ 視窗已關閉")
    
    # 顯示測試結果
    print("\n=== 測試結果 ===")
    if region_selector.has_region():
        print(f"✓ 成功設定多邊形區域")
        print(f"  頂點數量: {len(region_selector.polygon_points)}")
        print(f"  頂點座標: {region_selector.polygon_points}")
        
        # 測試區域功能
        bbox = region_selector.get_bounding_box()
        print(f"  邊界框: {bbox}")
        
        # 測試點在多邊形內的檢測
        test_points = [(200, 150), (400, 300), (500, 200)]
        print(f"  點在多邊形內測試:")
        for point in test_points:
            in_polygon = region_selector.point_in_polygon(point)
            print(f"    {point}: {'✓' if in_polygon else '✗'}")
    else:
        print("✗ 未設定選取區域")
    
    print("\n=== 功能驗證 ===")
    print("✓ 單一視窗操作")
    print("✓ 直接滑鼠選取（無需按鍵啟動）")
    print("✓ 即時頂點顯示")
    print("✓ 自動儲存/載入")
    print("✓ 簡化的操作流程")
    print("\n測試完成！")

def test_region_selector_only():
    """僅測試 RegionSelector 類別"""
    print("\n=== RegionSelector 類別測試 ===")
    
    try:
        region_selector = RegionSelector('test_class_only.json')
        print("✓ RegionSelector 初始化成功")
        
        # 測試基本方法
        print(f"✓ has_region(): {region_selector.has_region()}")
        print(f"✓ 多邊形頂點: {region_selector.polygon_points}")
        
        # 測試創建測試幀
        test_frame = create_test_video_frame(0)
        print(f"✓ 測試幀創建成功: {test_frame.shape}")
        
        # 測試繪製方法
        display_frame = region_selector.draw_region(test_frame)
        print(f"✓ draw_region() 方法正常: {display_frame.shape}")
        
        print("✓ 所有基本功能測試通過")
        
    except Exception as e:
        print(f"✗ RegionSelector 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 先測試類別基本功能
    test_region_selector_only()
    
    # 再測試完整的選取功能
    test_simplified_polygon_selection()
