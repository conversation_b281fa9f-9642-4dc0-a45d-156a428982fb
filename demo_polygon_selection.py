#!/usr/bin/env python3
"""
多邊形選取區域功能演示腳本
展示新的點擊式多邊形選取功能
"""

import cv2
import numpy as np
from gpu_optimized_head_detection import RegionSelector

def create_demo_image():
    """創建演示圖像"""
    img = np.zeros((600, 800, 3), dtype=np.uint8)
    
    # 添加背景網格
    for i in range(0, 800, 50):
        cv2.line(img, (i, 0), (i, 600), (30, 30, 30), 1)
    for i in range(0, 600, 50):
        cv2.line(img, (0, i), (800, i), (30, 30, 30), 1)
    
    # 添加一些模擬物件
    cv2.circle(img, (200, 150), 40, (100, 100, 255), -1)
    cv2.rectangle(img, (350, 200), (450, 300), (100, 255, 100), -1)
    cv2.circle(img, (600, 400), 60, (255, 100, 100), -1)
    
    # 添加標題和說明
    cv2.putText(img, 'Polygon Region Selection Demo', (50, 40), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    return img

def main():
    """主演示函數"""
    print("=== 多邊形選取區域功能演示 ===")
    print()
    print("操作說明:")
    print("1. 按 'r' 開始選取模式")
    print("2. 左鍵點擊添加頂點")
    print("3. 雙擊或點擊起始點完成選取")
    print("4. 按 'Enter' 也可完成選取")
    print("5. 按 'Esc' 取消當前選取")
    print("6. 按 'c' 清除選取區域")
    print("7. 按 'q' 退出演示")
    print()
    
    # 創建演示圖像
    demo_image = create_demo_image()
    
    # 初始化選取區域管理器
    region_selector = RegionSelector('demo_polygon_region.json')
    
    # 創建視窗
    window_name = 'Polygon Selection Demo'
    cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)
    cv2.setMouseCallback(window_name, region_selector.mouse_callback)
    
    print("演示開始！請在視窗中操作...")
    
    while True:
        # 繪製當前狀態
        display_image = region_selector.draw_region(demo_image)
        
        # 添加狀態資訊
        status_y = 550
        if region_selector.selecting:
            status = f"選取模式 - 已選擇 {len(region_selector.temp_points)} 個頂點"
            cv2.putText(display_image, status, (10, status_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        elif region_selector.has_region():
            status = f"已設定多邊形區域 - {len(region_selector.polygon_points)} 個頂點"
            cv2.putText(display_image, status, (10, status_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        else:
            status = "按 'r' 開始選取多邊形區域"
            cv2.putText(display_image, status, (10, status_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        # 添加按鍵提示
        cv2.putText(display_image, "R:選取 Enter:完成 Esc:取消 C:清除 Q:退出", 
                   (10, 580), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
        
        # 顯示圖像
        cv2.imshow(window_name, display_image)
        
        # 處理按鍵
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('r'):
            if not region_selector.selecting:
                region_selector.start_selection()
                print("開始選取模式")
            else:
                region_selector.stop_selection()
                print("停止選取模式")
        elif key == 27:  # Esc
            if region_selector.selecting:
                region_selector.cancel_selection()
                print("取消選取")
        elif key == 13:  # Enter
            if len(region_selector.temp_points) >= 3:
                region_selector._complete_selection()
                print("完成選取")
        elif key == ord('c'):
            region_selector.clear_region()
            print("清除選取區域")
    
    cv2.destroyAllWindows()
    
    # 顯示最終結果
    print("\n=== 演示結果 ===")
    if region_selector.has_region():
        print(f"✓ 成功設定多邊形區域")
        print(f"  頂點數量: {len(region_selector.polygon_points)}")
        print(f"  頂點座標: {region_selector.polygon_points}")
        
        bbox = region_selector.get_bounding_box()
        print(f"  邊界框: {bbox}")
        
        # 測試一些點是否在多邊形內
        test_points = [(200, 150), (400, 250), (600, 400)]
        print(f"  點在多邊形內測試:")
        for point in test_points:
            in_polygon = region_selector.point_in_polygon(point)
            print(f"    {point}: {'✓' if in_polygon else '✗'}")
    else:
        print("✗ 未設定選取區域")
    
    print("\n演示結束！")

if __name__ == "__main__":
    main()
