#!/usr/bin/env python3
"""
GPU优化的YOLOv5头部检测系统
专门针对GPU加速优化的RTSP视频流头部检测
"""

import cv2
import torch
import numpy as np
import time
from datetime import datetime
import argparse
import logging
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GPUOptimizedHeadDetector:
    def __init__(self, model_type='person', conf_threshold=0.4, device='cuda', batch_size=1):
        """
        GPU优化的头部检测器
        
        Args:
            model_type: 模型类型
            conf_threshold: 置信度阈值
            device: 运行设备
            batch_size: 批处理大小（GPU优化）
        """
        self.conf_threshold = conf_threshold
        self.device = device
        self.model_type = model_type
        self.batch_size = batch_size
        
        # 设置GPU优化参数
        self._setup_gpu_optimization()
        
        # 加载模型
        self._load_model()
        
    def _setup_gpu_optimization(self):
        """设置GPU优化参数"""
        if self.device == 'cuda' and torch.cuda.is_available():
            # 启用cudNN基准测试以优化卷积操作
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
            
            # 设置GPU内存分配策略
            torch.cuda.empty_cache()
            
            # 获取GPU信息
            gpu_count = torch.cuda.device_count()
            current_gpu = torch.cuda.current_device()
            gpu_name = torch.cuda.get_device_name(current_gpu)
            gpu_memory = torch.cuda.get_device_properties(current_gpu).total_memory / 1024**3
            
            logger.info(f"GPU优化设置:")
            logger.info(f"  - GPU数量: {gpu_count}")
            logger.info(f"  - 当前GPU: {current_gpu} ({gpu_name})")
            logger.info(f"  - GPU内存: {gpu_memory:.1f} GB")
            logger.info(f"  - CUDA版本: {torch.version.cuda}")
            logger.info(f"  - cuDNN启用: {torch.backends.cudnn.enabled}")
            logger.info(f"  - cuDNN基准: {torch.backends.cudnn.benchmark}")
            
        else:
            logger.warning("GPU不可用，使用CPU模式")
            self.device = 'cpu'
    
    def _load_model(self):
        """加载并优化模型"""
        try:
            logger.info("加载YOLOv5模型...")
            
            if self.model_type == 'custom':
                try:
                    # 尝试加载专门的头部检测模型
                    self.model = torch.hub.load('ultralytics/yolov5', 'custom', 
                                               path='yolov5s-head.pt', force_reload=True)
                    logger.info("加载专门的头部检测模型")
                except:
                    logger.warning("未找到头部检测模型，使用人体检测模型")
                    self.model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
                    self.model_type = 'person'
            else:
                self.model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
            
            # 模型优化设置
            self.model.to(self.device)
            self.model.conf = self.conf_threshold
            self.model.iou = 0.45  # NMS IoU阈值
            self.model.max_det = 1000  # 最大检测数量
            
            # GPU特定优化
            if self.device == 'cuda':
                # 启用半精度推理以提高速度
                self.model.half()
                logger.info("启用FP16半精度推理")
                
                # 预热GPU
                self._warmup_gpu()
            
            logger.info(f"模型加载完成，设备: {self.device}")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def _warmup_gpu(self):
        """GPU预热以获得稳定性能"""
        logger.info("GPU预热中...")
        dummy_input = torch.randn(1, 3, 640, 640).to(self.device)
        
        if self.device == 'cuda':
            dummy_input = dummy_input.half()
        
        # 进行几次推理来预热GPU
        with torch.no_grad():
            for _ in range(3):
                _ = self.model(dummy_input)
        
        torch.cuda.synchronize()
        logger.info("GPU预热完成")
    
    def _extract_head_region(self, frame, person_bbox, head_ratio=0.15):
        """从人体检测框中提取头部区域"""
        x1, y1, x2, y2 = person_bbox
        
        person_height = y2 - y1
        person_width = x2 - x1
        
        head_height = int(person_height * head_ratio)
        head_width_margin = int(person_width * 0.1)
        
        head_x1 = max(0, x1 + head_width_margin)
        head_y1 = max(0, y1)
        head_x2 = min(frame.shape[1], x2 - head_width_margin)
        head_y2 = min(frame.shape[0], y1 + head_height)
        
        return [head_x1, head_y1, head_x2, head_y2]
    
    def detect_heads_batch(self, frames):
        """批量检测头部（GPU优化）"""
        if not isinstance(frames, list):
            frames = [frames]
        
        results_list = []
        
        try:
            # GPU批量推理
            with torch.no_grad():
                if self.device == 'cuda':
                    torch.cuda.synchronize()  # 确保GPU操作完成
                
                # 批量推理
                results = self.model(frames)
                
                if self.device == 'cuda':
                    torch.cuda.synchronize()
            
            # 处理每帧的结果
            for i, frame in enumerate(frames):
                if len(frames) == 1:
                    frame_results = results
                else:
                    frame_results = [results[i]]
                
                head_count, head_bboxes, annotated_frame = self._process_single_frame_result(
                    frame, frame_results)
                results_list.append((head_count, head_bboxes, annotated_frame))
            
            return results_list
            
        except Exception as e:
            logger.error(f"批量检测过程中出错: {e}")
            return [(0, [], frame) for frame in frames]
    
    def _process_single_frame_result(self, frame, results):
        """处理单帧检测结果"""
        try:
            detections = results.pandas().xyxy[0]
            head_bboxes = []
            annotated_frame = frame.copy()
            
            if self.model_type == 'custom':
                head_detections = detections[detections['name'].isin(['head', 'person'])]
                
                for _, detection in head_detections.iterrows():
                    x1, y1, x2, y2 = int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])
                    confidence = detection['confidence']
                    head_bboxes.append([x1, y1, x2, y2, confidence])
            else:
                person_detections = detections[detections['class'] == 0]
                
                for _, detection in person_detections.iterrows():
                    person_bbox = [int(detection['xmin']), int(detection['ymin']), 
                                 int(detection['xmax']), int(detection['ymax'])]
                    confidence = detection['confidence']
                    
                    head_bbox = self._extract_head_region(frame, person_bbox)
                    head_bboxes.append(head_bbox + [confidence])
            
            # 绘制检测结果
            head_count = len(head_bboxes)
            
            for i, (x1, y1, x2, y2, confidence) in enumerate(head_bboxes):
                # 头部边界框（红色，最细线条）
                cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), (0, 0, 255), 1)
                
                # 只显示数字标签
                label = f'{i+1}'
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.4, 1)[0]
                cv2.rectangle(annotated_frame, (x1, y1 - label_size[1] - 6), 
                            (x1 + label_size[0] + 4, y1), (0, 0, 255), -1)
                cv2.putText(annotated_frame, label, (x1 + 2, y1 - 3), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
                
                # 中心点
                center_x = (x1 + x2) // 2
                center_y = (y1 + y2) // 2
                cv2.circle(annotated_frame, (center_x, center_y), 3, (255, 0, 0), -1)
            
            # 添加信息
            count_text = f'Head Count: {head_count}'
            cv2.putText(annotated_frame, count_text, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 3)
            
            mode_text = f'Mode: {self.model_type.upper()} (GPU)'
            cv2.putText(annotated_frame, mode_text, (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 165, 0), 2)
            
            # GPU信息
            if self.device == 'cuda':
                gpu_memory = torch.cuda.memory_allocated() / 1024**2  # MB
                gpu_text = f'GPU Memory: {gpu_memory:.0f}MB'
                cv2.putText(annotated_frame, gpu_text, (10, 110), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cv2.putText(annotated_frame, timestamp, (10, annotated_frame.shape[0] - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            return head_count, head_bboxes, annotated_frame
            
        except Exception as e:
            logger.error(f"处理帧结果时出错: {e}")
            return 0, [], frame

class GPUOptimizedRTSPProcessor:
    def __init__(self, rtsp_url, detector, save_video=False, output_path='gpu_head_detection.mp4'):
        self.rtsp_url = rtsp_url
        self.detector = detector
        self.save_video = save_video
        self.output_path = output_path
        self.cap = None
        self.out = None
        
        # 性能统计
        self.frame_times = []
        self.gpu_memory_usage = []
        
    def connect_stream(self):
        """连接RTSP流"""
        try:
            self.cap = cv2.VideoCapture(self.rtsp_url)
            
            # 优化设置
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            self.cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 5000)
            self.cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)
            
            if not self.cap.isOpened():
                raise Exception("无法连接到RTSP流")
            
            fps = int(self.cap.get(cv2.CAP_PROP_FPS)) or 25
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            logger.info(f"RTSP流连接成功: {width}x{height} @ {fps}fps")
            
            if self.save_video:
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                self.out = cv2.VideoWriter(self.output_path, fourcc, fps, (width, height))
                logger.info(f"GPU优化视频将保存到: {self.output_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"连接RTSP流失败: {e}")
            return False
    
    def process_stream(self, display=True, max_frames=None):
        """GPU优化的流处理"""
        if not self.connect_stream():
            return
        
        frame_count = 0
        start_time = time.time()
        
        try:
            while True:
                # 读取帧
                frame_start = time.time()
                ret, frame = self.cap.read()
                if not ret:
                    logger.warning("无法读取帧，尝试重新连接...")
                    time.sleep(1)
                    continue
                
                frame_count += 1
                
                # GPU检测
                detection_start = time.time()
                results = self.detector.detect_heads_batch([frame])
                head_count, head_bboxes, annotated_frame = results[0]
                detection_time = time.time() - detection_start
                
                # 记录性能
                frame_time = time.time() - frame_start
                self.frame_times.append(frame_time)
                
                if self.detector.device == 'cuda':
                    gpu_memory = torch.cuda.memory_allocated() / 1024**2
                    self.gpu_memory_usage.append(gpu_memory)
                
                # 计算性能指标
                elapsed_time = time.time() - start_time
                fps = frame_count / elapsed_time if elapsed_time > 0 else 0
                avg_detection_time = np.mean(self.frame_times[-30:]) if self.frame_times else 0
                
                # 添加性能信息
                fps_text = f'FPS: {fps:.1f} | Det: {detection_time*1000:.1f}ms'
                cv2.putText(annotated_frame, fps_text, (10, 150), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
                
                # 显示结果
                if display:
                    cv2.imshow('GPU优化头部检测', annotated_frame)
                    
                    key = cv2.waitKey(1) & 0xFF
                    if key == ord('q'):
                        break
                    elif key == ord('s'):
                        save_path = f"gpu_head_frame_{frame_count}.jpg"
                        cv2.imwrite(save_path, annotated_frame)
                        logger.info(f"保存帧到: {save_path}")
                    elif key == ord('m'):  # 显示GPU内存信息
                        if self.detector.device == 'cuda':
                            allocated = torch.cuda.memory_allocated() / 1024**2
                            cached = torch.cuda.memory_reserved() / 1024**2
                            logger.info(f"GPU内存 - 已分配: {allocated:.1f}MB, 已缓存: {cached:.1f}MB")
                
                # 保存视频
                if self.save_video and self.out:
                    self.out.write(annotated_frame)
                
                # 性能报告
                if frame_count % 100 == 0:
                    avg_fps = frame_count / elapsed_time
                    avg_gpu_memory = np.mean(self.gpu_memory_usage[-100:]) if self.gpu_memory_usage else 0
                    logger.info(f"帧 {frame_count}: 头部 {head_count}, "
                              f"平均FPS {avg_fps:.1f}, "
                              f"GPU内存 {avg_gpu_memory:.1f}MB")
                
                if max_frames and frame_count >= max_frames:
                    break
                    
        except KeyboardInterrupt:
            logger.info("用户中断程序")
        except Exception as e:
            logger.error(f"处理过程中出错: {e}")
        finally:
            self._print_performance_summary()
            self.cleanup()
    
    def _print_performance_summary(self):
        """打印性能总结"""
        if self.frame_times:
            avg_frame_time = np.mean(self.frame_times)
            avg_fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 0
            
            logger.info("=== GPU性能总结 ===")
            logger.info(f"平均帧处理时间: {avg_frame_time*1000:.1f}ms")
            logger.info(f"平均FPS: {avg_fps:.1f}")
            
            if self.gpu_memory_usage:
                avg_gpu_memory = np.mean(self.gpu_memory_usage)
                max_gpu_memory = max(self.gpu_memory_usage)
                logger.info(f"平均GPU内存使用: {avg_gpu_memory:.1f}MB")
                logger.info(f"峰值GPU内存使用: {max_gpu_memory:.1f}MB")
    
    def cleanup(self):
        """清理资源"""
        if self.cap:
            self.cap.release()
        if self.out:
            self.out.release()
        cv2.destroyAllWindows()
        
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        logger.info("资源清理完成")

def main():
    parser = argparse.ArgumentParser(description='GPU优化的YOLOv5头部检测系统')
    parser.add_argument('--rtsp-url', type=str, 
                       default='rtsp://root:Abc_123@*************:7040/axis-media/media.amp?resolution=1024x768',
                       help='RTSP流地址')
    parser.add_argument('--model-type', type=str, default='person',
                       choices=['custom', 'person'], help='模型类型')
    parser.add_argument('--conf-threshold', type=float, default=0.4,
                       help='检测置信度阈值')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cpu', 'cuda'], help='运行设备')
    parser.add_argument('--batch-size', type=int, default=1,
                       help='批处理大小（GPU优化）')
    parser.add_argument('--save-video', action='store_true',
                       help='保存输出视频')
    parser.add_argument('--output-path', type=str, default='gpu_head_detection.mp4',
                       help='输出视频路径')
    parser.add_argument('--no-display', action='store_true',
                       help='不显示视频窗口')
    parser.add_argument('--max-frames', type=int, default=None,
                       help='最大处理帧数')
    
    args = parser.parse_args()
    
    # GPU检查和设置
    if args.device == 'cuda':
        if torch.cuda.is_available():
            logger.info("=== GPU信息 ===")
            logger.info(f"PyTorch版本: {torch.__version__}")
            logger.info(f"CUDA版本: {torch.version.cuda}")
            logger.info(f"cuDNN版本: {torch.backends.cudnn.version()}")
            
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                logger.info(f"GPU {i}: {props.name}")
                logger.info(f"  - 内存: {props.total_memory/1024**3:.1f} GB")
                logger.info(f"  - 计算能力: {props.major}.{props.minor}")
        else:
            logger.error("CUDA不可用！请检查GPU驱动和CUDA安装")
            return 1
    
    try:
        # 初始化GPU优化检测器
        logger.info("初始化GPU优化头部检测器...")
        detector = GPUOptimizedHeadDetector(
            model_type=args.model_type,
            conf_threshold=args.conf_threshold,
            device=args.device,
            batch_size=args.batch_size
        )
        
        # 初始化GPU优化处理器
        logger.info("初始化GPU优化RTSP处理器...")
        processor = GPUOptimizedRTSPProcessor(
            rtsp_url=args.rtsp_url,
            detector=detector,
            save_video=args.save_video,
            output_path=args.output_path
        )
        
        # 开始GPU加速处理
        logger.info("开始GPU加速头部检测...")
        processor.process_stream(
            display=not args.no_display,
            max_frames=args.max_frames
        )
        
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())