#!/usr/bin/env python3
"""
測試選取區域功能的簡單腳本
"""

import cv2
import numpy as np
from gpu_optimized_head_detection import RegionSelector

def test_region_selector():
    """測試選取區域功能"""
    print("測試選取區域功能...")
    
    # 創建一個測試圖像
    test_image = np.zeros((600, 800, 3), dtype=np.uint8)
    cv2.putText(test_image, 'Test Image - Draw Region with Mouse', (50, 50), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    cv2.putText(test_image, 'Press ESC to exit', (50, 100), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    cv2.putText(test_image, 'Press C to clear region', (50, 130), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # 初始化選取區域管理器
    region_selector = RegionSelector('test_region.json')
    
    # 創建視窗並設定滑鼠回調
    cv2.namedWindow('Region Selection Test')
    cv2.setMouseCallback('Region Selection Test', region_selector.mouse_callback)
    
    print("操作說明:")
    print("- 用滑鼠拖拽選取區域")
    print("- 按 'c' 清除選取區域")
    print("- 按 ESC 退出")
    
    while True:
        # 繪製選取區域
        display_image = region_selector.draw_region(test_image)
        
        # 顯示圖像
        cv2.imshow('Region Selection Test', display_image)
        
        # 處理按鍵
        key = cv2.waitKey(1) & 0xFF
        if key == 27:  # ESC
            break
        elif key == ord('c'):
            region_selector.clear_region()
            print("選取區域已清除")
    
    cv2.destroyAllWindows()
    
    # 顯示最終結果
    if region_selector.has_region():
        print(f"最終選取區域: {region_selector.region}")
        
        # 測試裁切功能
        cropped = region_selector.crop_to_region(test_image)
        print(f"裁切後圖像大小: {cropped.shape}")
        
        # 測試座標調整功能
        test_detections = [[10, 10, 50, 50, 0.9], [100, 100, 150, 150, 0.8]]
        adjusted = region_selector.adjust_coordinates(test_detections)
        print(f"調整前座標: {test_detections}")
        print(f"調整後座標: {adjusted}")
    else:
        print("未設定選取區域")

if __name__ == "__main__":
    test_region_selector()
