#!/usr/bin/env python3
"""
測試多邊形選取區域功能的腳本
"""

import cv2
import numpy as np
from gpu_optimized_head_detection import RegionSelector

def test_polygon_region_selector():
    """測試多邊形選取區域功能"""
    print("測試多邊形選取區域功能...")

    # 創建一個測試圖像
    test_image = np.zeros((600, 800, 3), dtype=np.uint8)
    cv2.putText(test_image, 'Polygon Region Selection Test', (50, 50),
                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    cv2.putText(test_image, 'Press R to start selection', (50, 100),
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    cv2.putText(test_image, 'Left click to add points', (50, 130),
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    cv2.putText(test_image, 'Double-click to finish', (50, 160),
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    cv2.putText(test_image, 'Press C to clear, ESC to exit', (50, 190),
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

    # 初始化選取區域管理器
    region_selector = RegionSelector('test_polygon_region.json')

    # 創建視窗並設定滑鼠回調
    window_name = 'Polygon Region Selection Test'
    cv2.namedWindow(window_name)
    cv2.setMouseCallback(window_name, region_selector.mouse_callback)

    print("操作說明:")
    print("- 按 'r' 開始/停止選取模式")
    print("- 在選取模式下，左鍵點擊添加頂點")
    print("- 雙擊或點擊起始點完成選取")
    print("- 按 'Enter' 完成選取")
    print("- 按 'Esc' 取消當前選取")
    print("- 按 'c' 清除選取區域")
    print("- 按 'q' 退出")

    while True:
        # 繪製選取區域
        display_image = region_selector.draw_region(test_image)

        # 顯示圖像
        cv2.imshow(window_name, display_image)

        # 處理按鍵
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('r'):
            if not region_selector.selecting:
                region_selector.start_selection()
                print("開始選取模式")
            else:
                region_selector.stop_selection()
                print("停止選取模式")
        elif key == 27:  # ESC
            if region_selector.selecting:
                region_selector.cancel_selection()
                print("取消選取")
        elif key == 13:  # Enter
            if len(region_selector.temp_points) >= 3:
                region_selector._complete_selection()
                print("完成選取")
        elif key == ord('c'):
            region_selector.clear_region()
            print("選取區域已清除")

    cv2.destroyAllWindows()

    # 顯示最終結果
    if region_selector.has_region():
        print(f"最終多邊形選取區域: {len(region_selector.polygon_points)} 個頂點")
        print(f"頂點座標: {region_selector.polygon_points}")

        # 測試邊界框
        bbox = region_selector.get_bounding_box()
        print(f"邊界框: {bbox}")

        # 測試裁切功能
        cropped = region_selector.crop_to_region(test_image)
        print(f"裁切後圖像大小: {cropped.shape}")

        # 測試座標調整功能
        test_detections = [[10, 10, 50, 50, 0.9], [100, 100, 150, 150, 0.8]]
        adjusted = region_selector.adjust_coordinates(test_detections)
        print(f"調整前座標: {test_detections}")
        print(f"調整後座標: {adjusted}")

        # 測試點在多邊形內的檢測
        test_points = [(100, 100), (400, 300), (700, 500)]
        for point in test_points:
            in_polygon = region_selector.point_in_polygon(point)
            print(f"點 {point} 在多邊形內: {in_polygon}")
    else:
        print("未設定選取區域")

if __name__ == "__main__":
    test_polygon_region_selector()
