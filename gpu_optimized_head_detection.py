#!/usr/bin/env python3
"""
GPU优化的YOLOv5头部检测系统
专门针对GPU加速优化的RTSP视频流头部检测
"""

import cv2
import torch
import numpy as np
import time
from datetime import datetime
import argparse
import logging
import os
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RegionSelector:
    """簡化的多邊形選取區域管理器 - 直接滑鼠操作"""
    def __init__(self, config_file='detection_region.json'):
        self.config_file = config_file
        self.polygon_points = []  # 已完成的多邊形頂點列表
        self.temp_points = []     # 正在選取的臨時頂點
        self.current_mouse_pos = None  # 當前滑鼠位置
        self.is_selecting = False  # 是否正在選取過程中

        # 載入已儲存的區域設定
        self.load_region()

        if self.polygon_points:
            logger.info(f"已載入多邊形選取區域: {len(self.polygon_points)} 個頂點")

    def mouse_callback(self, event, x, y, flags, param):
        """簡化的滑鼠回調函數 - 直接操作"""
        self.current_mouse_pos = (x, y)

        if event == cv2.EVENT_LBUTTONDOWN:
            # 左鍵點擊添加頂點
            if len(self.temp_points) >= 3:
                # 檢查是否點擊在起始點附近（完成多邊形）
                start_point = self.temp_points[0]
                distance = np.sqrt((x - start_point[0])**2 + (y - start_point[1])**2)
                if distance < 15:  # 15像素內視為點擊起始點
                    self._complete_selection()
                    return

            # 添加新頂點
            self.temp_points.append((x, y))
            self.is_selecting = True
            logger.info(f"添加頂點 {len(self.temp_points)}: ({x}, {y})")

        elif event == cv2.EVENT_RBUTTONDOWN:
            # 右鍵點擊完成選取
            if len(self.temp_points) >= 3:
                self._complete_selection()

        elif event == cv2.EVENT_LBUTTONDBLCLK:
            # 雙擊完成選取
            if len(self.temp_points) >= 3:
                self._complete_selection()

    def _complete_selection(self):
        """完成多邊形選取"""
        if len(self.temp_points) >= 3:
            self.polygon_points = self.temp_points.copy()
            self.temp_points = []
            self.is_selecting = False
            self.save_region()
            logger.info(f"多邊形選取完成！共 {len(self.polygon_points)} 個頂點，已自動儲存")
        else:
            logger.warning("至少需要3個頂點才能形成多邊形")

    def clear_selection(self):
        """清除當前選取"""
        self.temp_points = []
        self.is_selecting = False
        logger.info("取消當前選取")

    def draw_region(self, frame):
        """在畫面上繪製選取區域"""
        display_frame = frame.copy()

        # 繪製已完成的多邊形區域
        if self.polygon_points and len(self.polygon_points) >= 3:
            points = np.array(self.polygon_points, np.int32)
            points = points.reshape((-1, 1, 2))
            cv2.polylines(display_frame, [points], True, (0, 255, 0), 2)

            # 添加標籤
            label_pos = self.polygon_points[0]
            cv2.putText(display_frame, 'Detection Region',
                       (label_pos[0], label_pos[1]-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

        # 繪製正在選取的多邊形
        if self.is_selecting and self.temp_points:
            # 繪製已確定的頂點
            for i, point in enumerate(self.temp_points):
                cv2.circle(display_frame, point, 5, (255, 0, 0), -1)
                cv2.putText(display_frame, str(i+1),
                           (point[0]+10, point[1]-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)

            # 繪製已連接的線段
            if len(self.temp_points) >= 2:
                for i in range(len(self.temp_points)-1):
                    cv2.line(display_frame, self.temp_points[i],
                            self.temp_points[i+1], (255, 0, 0), 2)

            # 繪製當前滑鼠位置到最後一個點的線段
            if len(self.temp_points) >= 1 and self.current_mouse_pos:
                cv2.line(display_frame, self.temp_points[-1],
                        self.current_mouse_pos, (255, 255, 0), 1)

            # 如果有3個以上頂點，繪製回到起始點的線段
            if len(self.temp_points) >= 3 and self.current_mouse_pos:
                start_point = self.temp_points[0]
                distance = np.sqrt((self.current_mouse_pos[0] - start_point[0])**2 +
                                 (self.current_mouse_pos[1] - start_point[1])**2)
                if distance < 15:
                    cv2.line(display_frame, self.temp_points[-1], start_point, (0, 255, 255), 2)
                    cv2.circle(display_frame, start_point, 8, (0, 255, 255), 2)

            # 添加選取提示
            cv2.putText(display_frame, 'Selecting Polygon...', (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
            cv2.putText(display_frame, f'Points: {len(self.temp_points)}', (10, 60),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
            if len(self.temp_points) >= 3:
                cv2.putText(display_frame, 'Double-click, right-click or click start point to finish', (10, 90),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 2)

        # 如果沒有選取區域且不在選取過程中，顯示提示
        elif not self.polygon_points:
            cv2.putText(display_frame, 'Click to select polygon region', (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        return display_frame

    def create_polygon_mask(self, frame_shape):
        """創建多邊形遮罩"""
        if not self.polygon_points or len(self.polygon_points) < 3:
            return None

        mask = np.zeros(frame_shape[:2], dtype=np.uint8)
        points = np.array(self.polygon_points, np.int32)
        cv2.fillPoly(mask, [points], 255)
        return mask

    def crop_to_region(self, frame):
        """將畫面裁切到選取區域 - 支援多邊形"""
        if not self.polygon_points or len(self.polygon_points) < 3:
            return frame

        # 創建遮罩
        mask = self.create_polygon_mask(frame.shape)
        if mask is None:
            return frame

        # 獲取多邊形的邊界框
        points = np.array(self.polygon_points)
        x_min, y_min = np.min(points, axis=0)
        x_max, y_max = np.max(points, axis=0)

        # 裁切到邊界框
        cropped_frame = frame[y_min:y_max, x_min:x_max]
        cropped_mask = mask[y_min:y_max, x_min:x_max]

        # 應用遮罩（多邊形外的區域設為黑色）
        if len(cropped_frame.shape) == 3:
            cropped_mask_3d = np.stack([cropped_mask] * 3, axis=2)
            cropped_frame = np.where(cropped_mask_3d > 0, cropped_frame, 0)
        else:
            cropped_frame = np.where(cropped_mask > 0, cropped_frame, 0)

        return cropped_frame

    def get_bounding_box(self):
        """獲取多邊形的邊界框"""
        if not self.polygon_points or len(self.polygon_points) < 3:
            return None

        points = np.array(self.polygon_points)
        x_min, y_min = np.min(points, axis=0)
        x_max, y_max = np.max(points, axis=0)
        return (x_min, y_min, x_max, y_max)

    def adjust_coordinates(self, detections):
        """調整檢測座標到原始畫面座標系 - 支援多邊形"""
        if not self.polygon_points or not detections:
            return detections

        bbox = self.get_bounding_box()
        if not bbox:
            return detections

        x_min, y_min, _, _ = bbox
        adjusted_detections = []

        for detection in detections:
            if len(detection) >= 4:
                adj_x1 = detection[0] + x_min
                adj_y1 = detection[1] + y_min
                adj_x2 = detection[2] + x_min
                adj_y2 = detection[3] + y_min

                if len(detection) > 4:
                    adjusted_detections.append([adj_x1, adj_y1, adj_x2, adj_y2] + list(detection[4:]))
                else:
                    adjusted_detections.append([adj_x1, adj_y1, adj_x2, adj_y2])

        return adjusted_detections

    def point_in_polygon(self, point):
        """檢查點是否在多邊形內"""
        if not self.polygon_points or len(self.polygon_points) < 3:
            return True

        x, y = point
        points = np.array(self.polygon_points, np.int32)
        return cv2.pointPolygonTest(points, (float(x), float(y)), False) >= 0

    def save_region(self):
        """儲存選取區域到檔案 - 支援多邊形"""
        if self.polygon_points and len(self.polygon_points) >= 3:
            try:
                config = {
                    'polygon_points': self.polygon_points,
                    'type': 'polygon',
                    'timestamp': datetime.now().isoformat()
                }
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                logger.info(f"多邊形選取區域已自動儲存到: {self.config_file}")
            except Exception as e:
                logger.error(f"儲存選取區域失敗: {e}")

    def load_region(self):
        """從檔案載入選取區域 - 支援多邊形"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                    # 支援新的多邊形格式
                    if 'polygon_points' in config:
                        self.polygon_points = config.get('polygon_points', [])
                        return True

                    # 向後相容舊的矩形格式
                    elif 'region' in config:
                        old_region = config.get('region', [])
                        if len(old_region) == 4:
                            x1, y1, x2, y2 = old_region
                            # 轉換矩形為多邊形
                            self.polygon_points = [(x1, y1), (x2, y1), (x2, y2), (x1, y2)]
                            logger.info(f"已載入並轉換舊格式選取區域為多邊形")
                            self.save_region()  # 儲存為新格式
                            return True

        except Exception as e:
            logger.error(f"載入選取區域失敗: {e}")
        return False

    def clear_region(self):
        """清除選取區域"""
        self.polygon_points = []
        self.temp_points = []
        self.is_selecting = False
        if os.path.exists(self.config_file):
            try:
                os.remove(self.config_file)
                logger.info("選取區域已清除")
            except Exception as e:
                logger.error(f"清除選取區域檔案失敗: {e}")

    def has_region(self):
        """檢查是否有設定選取區域"""
        return len(self.polygon_points) >= 3

class GPUOptimizedHeadDetector:
    def __init__(self, model_type='person', conf_threshold=0.1, device='cuda', batch_size=1, region_selector=None, detect_umbrella=True):
        """
        GPU优化的头部检测器

        Args:
            model_type: 模型类型
            conf_threshold: 置信度阈值
            device: 运行设备
            batch_size: 批处理大小（GPU优化）
            region_selector: 選取區域管理器
            detect_umbrella: 是否將雨傘也當作一個人來檢測
        """
        self.conf_threshold = conf_threshold
        self.device = device
        self.model_type = model_type
        self.batch_size = batch_size
        self.region_selector = region_selector
        self.detect_umbrella = detect_umbrella

        # COCO 類別定義
        self.coco_classes = {
            0: 'person',
            25: 'umbrella'  # 雨傘類別
        }

        # 设置GPU优化参数
        self._setup_gpu_optimization()

        # 加载模型
        self._load_model()
        
    def _setup_gpu_optimization(self):
        """设置GPU优化参数"""
        if self.device == 'cuda' and torch.cuda.is_available():
            # 启用cudNN基准测试以优化卷积操作
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
            
            # 设置GPU内存分配策略
            torch.cuda.empty_cache()
            
            # 获取GPU信息
            gpu_count = torch.cuda.device_count()
            current_gpu = torch.cuda.current_device()
            gpu_name = torch.cuda.get_device_name(current_gpu)
            gpu_memory = torch.cuda.get_device_properties(current_gpu).total_memory / 1024**3
            
            logger.info(f"GPU优化设置:")
            logger.info(f"  - GPU数量: {gpu_count}")
            logger.info(f"  - 当前GPU: {current_gpu} ({gpu_name})")
            logger.info(f"  - GPU内存: {gpu_memory:.1f} GB")
            logger.info(f"  - CUDA版本: {torch.version.cuda}")
            logger.info(f"  - cuDNN启用: {torch.backends.cudnn.enabled}")
            logger.info(f"  - cuDNN基准: {torch.backends.cudnn.benchmark}")
            
        else:
            logger.warning("GPU不可用，使用CPU模式")
            self.device = 'cpu'
    
    def _load_model(self):
        """加载并优化模型"""
        try:
            logger.info("加载YOLOv5模型...")
            
            if self.model_type == 'custom':
                try:
                    # 尝试加载专门的头部检测模型
                    self.model = torch.hub.load('ultralytics/yolov5', 'custom', 
                                               path='yolov5s-head.pt', force_reload=True)
                    logger.info("加载专门的头部检测模型")
                except:
                    logger.warning("未找到头部检测模型，使用人体检测模型")
                    self.model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
                    self.model_type = 'person'
            else:
                self.model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
            
            # 模型优化设置
            self.model.to(self.device)
            self.model.conf = self.conf_threshold
            self.model.iou = 0.45  # NMS IoU阈值
            self.model.max_det = 1000  # 最大检测数量
            
            # GPU特定优化
            if self.device == 'cuda':
                # 启用半精度推理以提高速度
                self.model.half()
                logger.info("启用FP16半精度推理")
                
                # 预热GPU
                self._warmup_gpu()
            
            logger.info(f"模型加载完成，设备: {self.device}")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def _warmup_gpu(self):
        """GPU预热以获得稳定性能"""
        logger.info("GPU预热中...")
        dummy_input = torch.randn(1, 3, 640, 640).to(self.device)
        
        if self.device == 'cuda':
            dummy_input = dummy_input.half()
        
        # 进行几次推理来预热GPU
        with torch.no_grad():
            for _ in range(3):
                _ = self.model(dummy_input)
        
        torch.cuda.synchronize()
        logger.info("GPU预热完成")
    
    def _extract_head_region(self, frame, person_bbox, head_ratio=0.15):
        """从人体检测框中提取头部区域"""
        x1, y1, x2, y2 = person_bbox
        
        person_height = y2 - y1
        person_width = x2 - x1
        
        head_height = int(person_height * head_ratio)
        head_width_margin = int(person_width * 0.1)
        
        head_x1 = max(0, x1 + head_width_margin)
        head_y1 = max(0, y1)
        head_x2 = min(frame.shape[1], x2 - head_width_margin)
        head_y2 = min(frame.shape[0], y1 + head_height)
        
        return [head_x1, head_y1, head_x2, head_y2]

    def _extract_umbrella_head_region(self, frame, umbrella_bbox):
        """從雨傘檢測框中提取假想的頭部區域"""
        x1, y1, x2, y2 = umbrella_bbox

        umbrella_width = x2 - x1
        umbrella_height = y2 - y1

        # 假設雨傘下方中央有一個人，頭部位置在雨傘正下方
        # 頭部大小設定為雨傘寬度的 1/4
        head_size = max(20, int(umbrella_width * 0.25))

        # 頭部位置：雨傘底部中央下方一點
        center_x = (x1 + x2) // 2
        head_top_y = y2  # 雨傘底部

        # 計算頭部邊界框
        head_x1 = max(0, center_x - head_size // 2)
        head_y1 = max(0, head_top_y)
        head_x2 = min(frame.shape[1], center_x + head_size // 2)
        head_y2 = min(frame.shape[0], head_top_y + head_size)

        return [head_x1, head_y1, head_x2, head_y2]
    
    def detect_heads_batch(self, frames, original_frames=None):
        """批量检测头部（GPU优化，支援選取區域）"""
        if not isinstance(frames, list):
            frames = [frames]

        if original_frames is None:
            original_frames = frames
        elif not isinstance(original_frames, list):
            original_frames = [original_frames]

        results_list = []

        try:
            # 如果有選取區域，先裁切畫面
            detection_frames = []
            for frame in frames:
                if self.region_selector and self.region_selector.has_region():
                    cropped_frame = self.region_selector.crop_to_region(frame)
                    detection_frames.append(cropped_frame)
                else:
                    detection_frames.append(frame)

            # GPU批量推理
            with torch.no_grad():
                if self.device == 'cuda':
                    torch.cuda.synchronize()  # 确保GPU操作完成

                # 批量推理
                results = self.model(detection_frames)

                if self.device == 'cuda':
                    torch.cuda.synchronize()

            # 处理每帧的结果
            for i, (detection_frame, original_frame) in enumerate(zip(detection_frames, original_frames)):
                if len(detection_frames) == 1:
                    frame_results = results
                else:
                    frame_results = [results[i]]

                head_count, head_bboxes, annotated_frame = self._process_single_frame_result(
                    detection_frame, frame_results, original_frame)
                results_list.append((head_count, head_bboxes, annotated_frame))

            return results_list

        except Exception as e:
            logger.error(f"批量检测过程中出错: {e}")
            return [(0, [], frame) for frame in original_frames]
    
    def _process_single_frame_result(self, detection_frame, results, original_frame=None):
        """处理单帧检测结果（支援選取區域）"""
        try:
            if original_frame is None:
                original_frame = detection_frame

            detections = results.pandas().xyxy[0]
            head_bboxes = []
            detection_types = []  # 記錄檢測類型：'person' 或 'umbrella'
            annotated_frame = original_frame.copy()
            
            if self.model_type == 'custom':
                head_detections = detections[detections['name'].isin(['head', 'person'])]

                for _, detection in head_detections.iterrows():
                    x1, y1, x2, y2 = int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])
                    confidence = detection['confidence']
                    head_bboxes.append([x1, y1, x2, y2, confidence])
                    detection_types.append('person')
            else:
                # 檢測人員 (class 0)
                person_detections = detections[detections['class'] == 0]

                for _, detection in person_detections.iterrows():
                    person_bbox = [int(detection['xmin']), int(detection['ymin']),
                                 int(detection['xmax']), int(detection['ymax'])]
                    confidence = detection['confidence']

                    head_bbox = self._extract_head_region(detection_frame, person_bbox)
                    head_bboxes.append(head_bbox + [confidence])
                    detection_types.append('person')

                # 如果啟用雨傘檢測，將雨傘也當作一個人
                if self.detect_umbrella:
                    umbrella_detections = detections[detections['class'] == 25]  # 雨傘類別

                    for _, detection in umbrella_detections.iterrows():
                        umbrella_bbox = [int(detection['xmin']), int(detection['ymin']),
                                       int(detection['xmax']), int(detection['ymax'])]
                        confidence = detection['confidence']

                        # 將雨傘的中心點當作頭部位置
                        umbrella_head_bbox = self._extract_umbrella_head_region(detection_frame, umbrella_bbox)
                        head_bboxes.append(umbrella_head_bbox + [confidence])
                        detection_types.append('umbrella')

            # 如果有選取區域，調整座標到原始畫面
            if self.region_selector and self.region_selector.has_region():
                head_bboxes = self.region_selector.adjust_coordinates(head_bboxes)
            
            # 绘制检测结果
            head_count = len(head_bboxes)
            person_count = detection_types.count('person') if detection_types else head_count
            umbrella_count = detection_types.count('umbrella') if detection_types else 0

            for i, (x1, y1, x2, y2, confidence) in enumerate(head_bboxes):
                # 根據檢測類型選擇顏色
                if i < len(detection_types):
                    detection_type = detection_types[i]
                    if detection_type == 'umbrella':
                        color = (255, 165, 0)  # 橙色表示雨傘
                        type_label = 'U'
                    else:
                        color = (0, 0, 255)    # 紅色表示人員
                        type_label = 'P'
                else:
                    color = (0, 0, 255)        # 預設紅色
                    type_label = 'P'

                # 头部边界框
                cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 1)

                # 顯示編號和類型標籤
                label = f'{i+1}{type_label}'
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.4, 1)[0]
                cv2.rectangle(annotated_frame, (x1, y1 - label_size[1] - 6),
                            (x1 + label_size[0] + 4, y1), color, -1)
                cv2.putText(annotated_frame, label, (x1 + 2, y1 - 3),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

                # 中心点
                center_x = (x1 + x2) // 2
                center_y = (y1 + y2) // 2
                cv2.circle(annotated_frame, (center_x, center_y), 3, color, -1)
            
            # 添加信息
            if self.detect_umbrella and umbrella_count > 0:
                count_text = f'Total: {head_count} (P:{person_count} U:{umbrella_count})'
                cv2.putText(annotated_frame, count_text, (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)

                # 添加圖例
                cv2.putText(annotated_frame, 'P=Person U=Umbrella', (10, 60),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            else:
                count_text = f'Head Count: {head_count}'
                cv2.putText(annotated_frame, count_text, (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 3)

            mode_text = f'Mode: {self.model_type.upper()} (GPU)'
            if self.detect_umbrella:
                mode_text += ' +Umbrella'
            cv2.putText(annotated_frame, mode_text, (10, 90),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 165, 0), 2)
            
            # GPU信息
            if self.device == 'cuda':
                gpu_memory = torch.cuda.memory_allocated() / 1024**2  # MB
                gpu_text = f'GPU Memory: {gpu_memory:.0f}MB'
                cv2.putText(annotated_frame, gpu_text, (10, 120),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cv2.putText(annotated_frame, timestamp, (10, annotated_frame.shape[0] - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            return head_count, head_bboxes, annotated_frame
            
        except Exception as e:
            logger.error(f"处理帧结果时出错: {e}")
            return 0, [], original_frame

class GPUOptimizedRTSPProcessor:
    def __init__(self, rtsp_url, detector, save_video=False, output_path='gpu_head_detection.mp4', region_selector=None):
        self.rtsp_url = rtsp_url
        self.detector = detector
        self.save_video = save_video
        self.output_path = output_path
        self.cap = None
        self.out = None
        self.region_selector = region_selector

        # 性能统计
        self.frame_times = []
        self.gpu_memory_usage = []
        
    def connect_stream(self):
        """连接RTSP流"""
        try:
            self.cap = cv2.VideoCapture(self.rtsp_url)
            
            # 优化设置
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            self.cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 5000)
            self.cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)
            
            if not self.cap.isOpened():
                raise Exception("无法连接到RTSP流")
            
            fps = int(self.cap.get(cv2.CAP_PROP_FPS)) or 25
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            logger.info(f"RTSP流连接成功: {width}x{height} @ {fps}fps")
            
            if self.save_video:
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                self.out = cv2.VideoWriter(self.output_path, fourcc, fps, (width, height))
                logger.info(f"GPU优化视频将保存到: {self.output_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"连接RTSP流失败: {e}")
            return False
    
    def process_stream(self, display=True, max_frames=None):
        """GPU优化的流处理（簡化的多邊形選取操作）"""
        if not self.connect_stream():
            return

        frame_count = 0
        start_time = time.time()
        window_name = 'GPU_Head_Detection'  # 強制使用 ASCII 字串

        # 在開始處理前就創建視窗並設定滑鼠回調，避免後續重複創建
        if display:
            # 先清理所有可能存在的視窗
            cv2.destroyAllWindows()
            cv2.waitKey(100)  # 增加等待時間確保視窗完全關閉

            # 創建唯一的主視窗
            cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)

            # 設定視窗屬性，防止自動創建新視窗
            cv2.setWindowProperty(window_name, cv2.WND_PROP_AUTOSIZE, cv2.WINDOW_AUTOSIZE)

            if self.region_selector:
                cv2.setMouseCallback(window_name, self.region_selector.mouse_callback)
                logger.info("✓ 主視窗已創建，滑鼠可直接進行多邊形選取")
                logger.info("簡化多邊形選取操作說明:")
                logger.info("- 直接用滑鼠左鍵點擊添加多邊形頂點")
                logger.info("- 雙擊或右鍵完成選取")
                logger.info("- 按 'c' 清除選取區域")
                logger.info("- 按 'q' 退出程式")

            # 顯示一個初始幀以確保視窗正確創建
            initial_frame = np.zeros((480, 640, 3), dtype=np.uint8)
            cv2.putText(initial_frame, 'Initializing...', (250, 240),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            cv2.imshow(window_name, initial_frame)
            cv2.waitKey(100)

        try:
            while True:
                # 读取帧
                frame_start = time.time()
                ret, frame = self.cap.read()
                if not ret:
                    logger.warning("无法读取帧，尝试重新连接...")
                    time.sleep(1)
                    continue

                frame_count += 1

                # GPU检测
                detection_start = time.time()
                results = self.detector.detect_heads_batch([frame])
                head_count, head_bboxes, annotated_frame = results[0]
                detection_time = time.time() - detection_start
                
                # 记录性能
                frame_time = time.time() - frame_start
                self.frame_times.append(frame_time)
                
                if self.detector.device == 'cuda':
                    gpu_memory = torch.cuda.memory_allocated() / 1024**2
                    self.gpu_memory_usage.append(gpu_memory)
                
                # 计算性能指标
                elapsed_time = time.time() - start_time
                fps = frame_count / elapsed_time if elapsed_time > 0 else 0
                avg_detection_time = np.mean(self.frame_times[-30:]) if self.frame_times else 0
                
                # 添加性能信息
                fps_text = f'FPS: {fps:.1f} | Det: {detection_time*1000:.1f}ms'
                cv2.putText(annotated_frame, fps_text, (10, 150),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)

                # 繪製選取區域（如果有的話）
                if self.region_selector:
                    annotated_frame = self.region_selector.draw_region(annotated_frame)

                # 显示结果
                if display:
                    cv2.imshow(window_name, annotated_frame)

                    key = cv2.waitKey(1) & 0xFF
                    if key == ord('q'):
                        break
                    elif key == ord('s'):
                        save_path = f"gpu_head_frame_{frame_count}.jpg"
                        cv2.imwrite(save_path, annotated_frame)
                        logger.info(f"保存帧到: {save_path}")
                    elif key == ord('m'):  # 显示GPU内存信息
                        if self.detector.device == 'cuda':
                            allocated = torch.cuda.memory_allocated() / 1024**2
                            cached = torch.cuda.memory_reserved() / 1024**2
                            logger.info(f"GPU内存 - 已分配: {allocated:.1f}MB, 已缓存: {cached:.1f}MB")
                    elif key == ord('c') and self.region_selector:  # 清除選取區域
                        self.region_selector.clear_region()
                        logger.info("選取區域已清除")
                    elif key == 27 and self.region_selector:  # Esc - 取消當前選取
                        self.region_selector.clear_selection()
                        logger.info("取消當前選取")
                
                # 保存视频
                if self.save_video and self.out:
                    self.out.write(annotated_frame)
                
                # 性能报告
                if frame_count % 100 == 0:
                    avg_fps = frame_count / elapsed_time
                    avg_gpu_memory = np.mean(self.gpu_memory_usage[-100:]) if self.gpu_memory_usage else 0
                    logger.info(f"帧 {frame_count}: 头部 {head_count}, "
                              f"平均FPS {avg_fps:.1f}, "
                              f"GPU内存 {avg_gpu_memory:.1f}MB")
                
                if max_frames and frame_count >= max_frames:
                    break
                    
        except KeyboardInterrupt:
            logger.info("用户中断程序")
        except Exception as e:
            logger.error(f"处理过程中出错: {e}")
        finally:
            self._print_performance_summary()
            self.cleanup()
    
    def _print_performance_summary(self):
        """打印性能总结"""
        if self.frame_times:
            avg_frame_time = np.mean(self.frame_times)
            avg_fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 0
            
            logger.info("=== GPU性能总结 ===")
            logger.info(f"平均帧处理时间: {avg_frame_time*1000:.1f}ms")
            logger.info(f"平均FPS: {avg_fps:.1f}")
            
            if self.gpu_memory_usage:
                avg_gpu_memory = np.mean(self.gpu_memory_usage)
                max_gpu_memory = max(self.gpu_memory_usage)
                logger.info(f"平均GPU内存使用: {avg_gpu_memory:.1f}MB")
                logger.info(f"峰值GPU内存使用: {max_gpu_memory:.1f}MB")
    
    def cleanup(self):
        """清理资源"""
        if self.cap:
            self.cap.release()
        if self.out:
            self.out.release()
        cv2.destroyAllWindows()
        
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        logger.info("资源清理完成")

def main():
    parser = argparse.ArgumentParser(description='GPU优化的YOLOv5头部检测系统')
    parser.add_argument('--rtsp-url', type=str, 
                       default='rtsp://root:Abc_123@*************:7040/axis-media/media.amp?resolution=1024x768',
                       help='RTSP流地址')
    parser.add_argument('--model-type', type=str, default='person',
                       choices=['custom', 'person'], help='模型类型')
    parser.add_argument('--conf-threshold', type=float, default=0.4,
                       help='检测置信度阈值')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cpu', 'cuda'], help='运行设备')
    parser.add_argument('--batch-size', type=int, default=1,
                       help='批处理大小（GPU优化）')
    parser.add_argument('--save-video', action='store_true',
                       help='保存输出视频')
    parser.add_argument('--output-path', type=str, default='gpu_head_detection.mp4',
                       help='输出视频路径')
    parser.add_argument('--no-display', action='store_true',
                       help='不显示视频窗口')
    parser.add_argument('--max-frames', type=int, default=None,
                       help='最大处理帧数')
    parser.add_argument('--detect-umbrella', action='store_true',
                       help='啟用雨傘檢測，將雨傘也當作一個人來計算')
    parser.add_argument('--no-umbrella', action='store_true',
                       help='禁用雨傘檢測（預設啟用）')
    
    args = parser.parse_args()
    
    # GPU检查和设置
    if args.device == 'cuda':
        if torch.cuda.is_available():
            logger.info("=== GPU信息 ===")
            logger.info(f"PyTorch版本: {torch.__version__}")
            logger.info(f"CUDA版本: {torch.version.cuda}")
            logger.info(f"cuDNN版本: {torch.backends.cudnn.version()}")
            
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                logger.info(f"GPU {i}: {props.name}")
                logger.info(f"  - 内存: {props.total_memory/1024**3:.1f} GB")
                logger.info(f"  - 计算能力: {props.major}.{props.minor}")
        else:
            logger.error("CUDA不可用！请检查GPU驱动和CUDA安装")
            return 1
    
    try:
        # 初始化簡化的多邊形選取區域管理器
        logger.info("初始化多邊形選取區域管理器...")
        region_selector = RegionSelector()

        # 決定是否啟用雨傘檢測
        detect_umbrella = not args.no_umbrella if hasattr(args, 'no_umbrella') else True
        if hasattr(args, 'detect_umbrella') and args.detect_umbrella:
            detect_umbrella = True

        # 初始化GPU优化检测器
        logger.info("初始化GPU优化头部检测器...")
        if detect_umbrella:
            logger.info("雨傘檢測已啟用 - 雨傘將被當作一個人來計算")
        else:
            logger.info("雨傘檢測已禁用")

        detector = GPUOptimizedHeadDetector(
            model_type=args.model_type,
            conf_threshold=args.conf_threshold,
            device=args.device,
            batch_size=args.batch_size,
            region_selector=region_selector,
            detect_umbrella=detect_umbrella
        )

        # 初始化GPU优化处理器
        logger.info("初始化GPU优化RTSP处理器...")
        processor = GPUOptimizedRTSPProcessor(
            rtsp_url=args.rtsp_url,
            detector=detector,
            save_video=args.save_video,
            output_path=args.output_path,
            region_selector=region_selector
        )
        
        # 开始GPU加速处理
        logger.info("开始GPU加速头部检测...")
        processor.process_stream(
            display=not args.no_display,
            max_frames=args.max_frames
        )
        
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())