#!/usr/bin/env python3
"""
測試 ASCII 視窗名稱功能
"""

import cv2
import numpy as np
import time
from gpu_optimized_head_detection import RegionSelector

def test_ascii_window_name():
    """測試 ASCII 視窗名稱"""
    print("=== ASCII 視窗名稱測試 ===")
    print()
    print("測試目標:")
    print("✓ 確保視窗名稱使用純 ASCII 字符")
    print("✓ 避免編碼問題")
    print("✓ 提高跨平台相容性")
    print("✓ 滑鼠回調功能正常")
    print()
    
    # 測試不同的視窗名稱
    test_cases = [
        ('GPU_Head_Detection', '主程式視窗名稱'),
        ('ASCII_Test_Window', '測試視窗名稱'),
        ('Simple_Window_123', '包含數字的視窗名稱'),
        ('Window_With_Underscores', '包含底線的視窗名稱')
    ]
    
    for window_name, description in test_cases:
        print(f"測試: {description}")
        print(f"視窗名稱: '{window_name}'")
        
        # 檢查是否為純 ASCII
        is_ascii = all(ord(c) < 128 for c in window_name)
        print(f"ASCII 檢查: {'✓ 通過' if is_ascii else '✗ 失敗'}")
        
        # 測試視窗創建
        try:
            cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)
            
            # 創建測試圖像
            test_image = np.zeros((300, 500, 3), dtype=np.uint8)
            cv2.putText(test_image, f'Window: {window_name}', (50, 150), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(test_image, 'ASCII Window Name Test', (50, 200), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
            cv2.putText(test_image, 'Press any key to continue', (50, 250), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)
            
            cv2.imshow(window_name, test_image)
            print(f"視窗創建: ✓ 成功")
            
            # 等待用戶確認
            cv2.waitKey(1000)  # 顯示1秒
            
            # 清理視窗
            cv2.destroyWindow(window_name)
            cv2.waitKey(1)
            print(f"視窗清理: ✓ 完成")
            
        except Exception as e:
            print(f"視窗操作: ✗ 失敗 - {e}")
        
        print()
    
    print("=== 主程式視窗名稱測試 ===")
    
    # 測試主程式使用的視窗名稱
    main_window_name = 'GPU_Head_Detection'
    region_selector = RegionSelector('test_ascii_window.json')
    
    print(f"主程式視窗名稱: '{main_window_name}'")
    print(f"ASCII 檢查: {'✓ 通過' if all(ord(c) < 128 for c in main_window_name) else '✗ 失敗'}")
    
    try:
        # 清理現有視窗
        cv2.destroyAllWindows()
        cv2.waitKey(100)
        
        # 創建主視窗
        cv2.namedWindow(main_window_name, cv2.WINDOW_AUTOSIZE)
        cv2.setWindowProperty(main_window_name, cv2.WND_PROP_AUTOSIZE, cv2.WINDOW_AUTOSIZE)
        cv2.setMouseCallback(main_window_name, region_selector.mouse_callback)
        
        print("主視窗創建: ✓ 成功")
        print("滑鼠回調設定: ✓ 完成")
        
        # 創建測試幀
        frame_count = 0
        print("\n開始互動測試...")
        print("操作說明:")
        print("- 左鍵點擊添加多邊形頂點")
        print("- 雙擊或右鍵完成選取")
        print("- 按 'c' 清除選取區域")
        print("- 按 'q' 退出測試")
        
        while True:
            # 創建動態測試幀
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            frame[:] = (40, 40, 40)
            
            # 添加移動的測試物件
            t = frame_count * 0.1
            x = int(320 + 100 * np.sin(t))
            y = int(240 + 50 * np.cos(t))
            cv2.circle(frame, (x, y), 30, (100, 255, 100), -1)
            
            # 繪製選取區域
            display_frame = region_selector.draw_region(frame)
            
            # 添加資訊
            cv2.putText(display_frame, f'ASCII Window: {main_window_name}', (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            if region_selector.has_region():
                status = f"Region Set: {len(region_selector.polygon_points)} points"
                cv2.putText(display_frame, status, (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            elif region_selector.is_selecting:
                status = f"Selecting: {len(region_selector.temp_points)} points"
                cv2.putText(display_frame, status, (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
            else:
                cv2.putText(display_frame, "Click to start polygon selection", (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # 添加編碼資訊
            cv2.putText(display_frame, "Window name encoding: ASCII", (10, 450), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
            
            # 顯示幀
            cv2.imshow(main_window_name, display_frame)
            
            # 處理按鍵
            key = cv2.waitKey(30) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('c'):
                region_selector.clear_region()
                print("✓ 選取區域已清除")
            
            frame_count += 1
            time.sleep(0.033)
        
        print("互動測試: ✓ 完成")
        
    except Exception as e:
        print(f"主視窗測試: ✗ 失敗 - {e}")
    
    finally:
        cv2.destroyAllWindows()
        cv2.waitKey(100)
        print("視窗清理: ✓ 完成")
    
    print("\n=== 測試總結 ===")
    print("✓ 視窗名稱已改為純 ASCII 字符")
    print("✓ 避免了潛在的編碼問題")
    print("✓ 提高了跨平台相容性")
    print("✓ 滑鼠回調功能正常")
    print("✓ 多邊形選取功能正常")
    
    # 顯示字符編碼資訊
    print(f"\n視窗名稱字符分析:")
    for char in main_window_name:
        print(f"  '{char}' -> ASCII {ord(char)}")
    
    print(f"\n原始中文視窗名稱字符分析:")
    chinese_name = 'GPU优化头部检测'
    for char in chinese_name:
        print(f"  '{char}' -> Unicode {ord(char)}")
    
    print("\nASCII 視窗名稱修改完成！")

def test_window_name_compatibility():
    """測試視窗名稱相容性"""
    print("\n=== 視窗名稱相容性測試 ===")
    
    # 測試各種 ASCII 字符組合
    test_names = [
        'Simple',
        'With_Underscores',
        'With-Dashes',
        'With123Numbers',
        'MixedCaseWindow',
        'window_with_lowercase',
        'WINDOW_WITH_UPPERCASE'
    ]
    
    for name in test_names:
        try:
            cv2.namedWindow(name, cv2.WINDOW_AUTOSIZE)
            test_img = np.zeros((100, 300, 3), dtype=np.uint8)
            cv2.putText(test_img, name, (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.imshow(name, test_img)
            cv2.waitKey(100)
            cv2.destroyWindow(name)
            print(f"✓ '{name}' - 相容")
        except Exception as e:
            print(f"✗ '{name}' - 失敗: {e}")
    
    cv2.destroyAllWindows()
    print("相容性測試完成")

if __name__ == "__main__":
    test_ascii_window_name()
    test_window_name_compatibility()
