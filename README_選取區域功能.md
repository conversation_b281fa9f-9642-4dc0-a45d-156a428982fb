# GPU優化頭部檢測系統 - 選取區域功能

## 新增功能概述

本次更新為 `gpu_optimized_head_detection.py` 添加了選取區域功能，讓您可以指定特定區域進行頭部檢測，提高檢測效率並減少誤檢。

## 主要功能

### 1. 選取區域設定
- **滑鼠拖拽選取**: 在視頻畫面上用滑鼠拖拽選取檢測區域
- **即時預覽**: 選取過程中會顯示即時預覽框
- **自動儲存**: 選取完成後自動儲存到 `detection_region.json` 檔案
- **自動載入**: 下次執行時自動載入上次儲存的選取區域

### 2. 區域檢測
- **限制檢測範圍**: 只在選取區域內進行頭部檢測
- **座標自動調整**: 檢測結果座標自動調整回原始畫面座標系
- **效能提升**: 減少檢測範圍可提高處理速度

### 3. 視覺化顯示
- **區域邊框**: 綠色邊框顯示當前選取區域
- **區域標籤**: 顯示 "Detection Region" 標籤
- **選取提示**: 選取過程中顯示 "Selecting..." 提示

## 操作說明

### 基本操作
1. **啟動程式**: 執行 `python gpu_optimized_head_detection.py`
2. **進入選取模式**: 按 `r` 鍵進入選取區域模式
3. **選取區域**: 用滑鼠左鍵拖拽選取檢測區域
4. **退出選取模式**: 按 `r` 鍵退出選取區域模式
5. **清除區域**: 按 `c` 鍵清除當前選取區域
6. **退出程式**: 按 `q` 鍵退出

### 按鍵說明
- `r`: 進入/退出選取區域模式
- `c`: 清除選取區域
- `q`: 退出程式
- `s`: 儲存當前畫面
- `m`: 顯示GPU記憶體資訊

### 滑鼠操作
- **左鍵拖拽**: 選取檢測區域
- **拖拽過程**: 顯示即時選取框
- **釋放左鍵**: 完成選取並自動儲存

## 檔案說明

### 新增檔案
- `detection_region.json`: 儲存選取區域設定的檔案
- `test_region_selection.py`: 測試選取區域功能的腳本
- `README_選取區域功能.md`: 本說明文件

### 修改的類別

#### RegionSelector 類別
- 管理選取區域的所有功能
- 處理滑鼠事件和區域選取
- 負責區域設定的儲存和載入

#### GPUOptimizedHeadDetector 類別
- 新增 `region_selector` 參數
- 支援在選取區域內進行檢測
- 自動調整檢測結果座標

#### GPUOptimizedRTSPProcessor 類別
- 整合選取區域功能
- 處理選取模式的顯示和按鍵
- 支援即時區域設定

## 設定檔格式

`detection_region.json` 檔案格式：
```json
{
  "region": [x1, y1, x2, y2],
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

其中：
- `region`: 選取區域座標 [左上角x, 左上角y, 右下角x, 右下角y]
- `timestamp`: 設定時間戳記

## 測試功能

執行測試腳本：
```bash
python test_region_selection.py
```

測試腳本功能：
- 測試選取區域的基本功能
- 驗證座標調整功能
- 測試區域儲存和載入

## 注意事項

1. **區域大小**: 選取區域必須大於 50x50 像素才會生效
2. **座標系統**: 所有座標都是基於原始畫面的絕對座標
3. **效能影響**: 選取較小區域可以提高檢測速度
4. **檔案權限**: 確保程式有權限讀寫 `detection_region.json` 檔案

## 故障排除

### 常見問題
1. **選取區域無效**: 確保選取區域大小足夠（>50x50像素）
2. **設定檔無法儲存**: 檢查檔案權限和磁碟空間
3. **座標偏移**: 確保在正確的視窗中進行選取操作

### 除錯模式
程式會在控制台輸出詳細的操作日誌，包括：
- 選取區域的座標資訊
- 檔案儲存和載入狀態
- 檢測結果的座標調整過程

## 效能優化建議

1. **合理選取區域**: 選取包含主要檢測目標的最小區域
2. **避免頻繁切換**: 減少進出選取模式的頻率
3. **定期清理**: 不需要時及時清除選取區域以使用全畫面檢測
